package gr.datamation.mx.report

import PaymentReturnTest
import gr.datamation.mx.Message
import gr.datamation.mx.report.model.InternalData
import org.junit.jupiter.api.*
import java.io.File
import java.io.IOException
import java.nio.file.Path
import java.nio.file.Paths

object NoOpDispatcher : IDispatcher {
    /* no-op */
    override fun createReport(
        message: Any,
        outputFile: File,
        resourceBasePath: String,
        internalData: InternalData,
        externalData: Map<String, String>
    ) {
        // No report from a NoOp dispatcher
        // this is for unit testing only
    }

    override fun createByteArrayReport(
        message: Any,
        resourceBasePath: String,
        internalData: InternalData,
        externalData: Map<String, String>
    ): ByteArray {
        // No report from a NoOp dispatcher
        // this is for unit testing only
        return ByteArray(0)
    }
}

val RESOURCE_BASE_PATH: Path = Paths.get("./src/test/resources")

class ReporterTest {

    val internalData = InternalData("test", "test")

    @Nested
    @DisplayName("when xml parsing fails")
    inner class WithInvalidInputPath {
        private val inputPath = Paths.get("input file")
        private val outputPath = Paths.get("output file")

        private val dispatcher: IDispatcher = NoOpDispatcher

        private val parser: IParser = object : IParser {
            override fun parseXML(path: Path): Message<*, *, *> {
                throw IOException("Bad path")
            }
        }

        private val reporter = Reporter(
            parser = parser,
            dispatcher = dispatcher,
            resourceBasePath = RESOURCE_BASE_PATH
        )

        @Test
        fun `should throw illegal argument exception`() {
            assertThrows<IOException> {
                reporter.buildReport(
                    inputFilePath = inputPath,
                    outputFilePath = outputPath,
                    internalData = internalData,
                )
            }
        }
    }

    @Nested
    @DisplayName("when xml parsed successfully")
    inner class WithValidInputPath {
        private val inputPath = Paths.get("input path")
        private val outputPath = Paths.get("output path")

        private val parser: IParser = object : IParser {
            override fun parseXML(path: Path): Message<*, *, *> {
                return PaymentReturnTest.createMessage()
            }
        }

        @Nested
        @DisplayName("when message type is supported")
        inner class WithSupportedMsgType {
            private val dispatcher: IDispatcher = NoOpDispatcher

            private val reporter = Reporter(
                parser = parser,
                dispatcher = dispatcher,
                resourceBasePath = RESOURCE_BASE_PATH
            )

            @Test
            fun `should build report`() {
                assertDoesNotThrow {
                    reporter.buildReport(
                        inputFilePath = inputPath,
                        outputFilePath = outputPath,
                        internalData = internalData,
                    )
                }
            }
        }

        @Nested
        @DisplayName("when message type is not supported")
        inner class WithUnsupportedMsgType {
            private val dispatcher: IDispatcher = object : IDispatcher {
                override fun createReport(
                    message: Any,
                    outputFile: File,
                    resourceBasePath: String,
                    internalData: InternalData,
                    externalData: Map<String, String>
                ) {
                    throw IllegalArgumentException("Bad type")
                }

                override fun createByteArrayReport(
                    message: Any,
                    resourceBasePath: String,
                    internalData: InternalData,
                    externalData: Map<String, String>
                ): ByteArray {
                    throw IllegalArgumentException("Bad type")
                }
            }

            private val reporter = Reporter(
                parser = parser,
                dispatcher = dispatcher,
                resourceBasePath = RESOURCE_BASE_PATH
            )

            @Test
            fun `should throw illegal argument exception`() {
                assertThrows<IllegalArgumentException> {
                    reporter.buildReport(
                        inputFilePath = inputPath,
                        outputFilePath = outputPath,
                        internalData = internalData,
                    )
                }
            }
        }

        @Nested
        @DisplayName("when message is given as xml text")
        inner class WithMessageAsXmlText {
            private val dispatcher: IDispatcher = NoOpDispatcher

            private val reporter = Reporter(
                parser = parser,
                dispatcher = dispatcher,
                resourceBasePath = RESOURCE_BASE_PATH
            )

            @Test
            fun `should build report`() {
                val xmlText: String = object {}.javaClass.getResource("/samples/camt.053.001.01.xml")?.readText()!!

                assertDoesNotThrow {
                    reporter.buildReport(
                        messageText = xmlText,
                        outputFilePath = outputPath,
                        internalData = internalData,
                    )
                }
            }
        }
    }
}