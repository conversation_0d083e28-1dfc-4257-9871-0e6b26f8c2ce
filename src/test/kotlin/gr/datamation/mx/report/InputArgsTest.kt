package gr.datamation.mx.report

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows

class InputArgsTest {

    @Test
    fun `returns parsed arguments`() {
        val args =
            InputArgs.parse(
                arrayOf(
                    "-i", "testInputFile",
                    "-o", "testOutputFile",
                    "-id", "testID",
                    "-is", "testIS"
                )
            )
        Assertions.assertEquals(args.inputFile.toString(), "testInputFile")
        Assertions.assertEquals(args.outputFile.toString(), "testOutputFile")
        Assertions.assertEquals(args.internalId, "testID")
        Assertions.assertEquals(args.internalStatus, "testIS")
    }

    @Test
    fun `throw if required argument is empty`() {
        assertThrows<IllegalArgumentException> {
            val parsed = InputArgs.parse(arrayOf("-o", "testOutputFile", "-d", "inputFile"))
            println(parsed)
        }
    }

    @Test
    fun `throw if uneven number of arguments is passed`() {
        assertThrows<IllegalArgumentException> {
            InputArgs.parse(arrayOf("-i", "testInputFile", "odd element"))
        }
    }

    @Test
    fun `does not throw if unknown argument is passed`() {
        assertDoesNotThrow {
            InputArgs.parse(
                arrayOf(
                    "-i", "testInputFile",
                    "-o", "testOutputFile",
                    "-id", "testID",
                    "-is", "testIS",
                    "-d", "unknownArgument"
                )
            )
        }
    }
}