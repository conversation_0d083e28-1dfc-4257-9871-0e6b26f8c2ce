package e2e

import gr.datamation.mx.report.IParser
import gr.datamation.mx.report.Reporter
import gr.datamation.mx.report.model.InternalData
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail
import util.measureTest
import java.io.File
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.util.stream.Collectors

/**
 * These are potentially long-running tests, consider disabling in CI/CD.
 * They pick all XML files from the test/resources/samples dir
 * and create reports from each one of them.
 */
class EndToEndTests {
    private var tempDir: Path = Files.createTempDirectory("tmp").toAbsolutePath()
    private val resourcesDir = Paths.get("./src/test/resources")

    @Test
    fun testXmlCamt053Reports() {
        testXmlWithPrefix("camt.053")
    }

    @Test
    fun testXmlCamt054Reports() {
        testXmlWithPrefix("camt.054")
    }

    @Test
    fun testXmlPacs004Reports() {
        testXmlWithPrefix("pacs.004")
    }

    @Test
    fun testXmlPacs008Reports() {
        testXmlWithPrefix("pacs.008")
    }

    @Test
    fun testXmlPacs009Reports() {
        testXmlWithPrefix("pacs.009")
    }

    private fun testXmlWithPrefix(prefix: String) {
        println("Output directory $tempDir")

        val inputList = Files.list(Paths.get(resourcesDir.toString(), "samples"))
            .filter {
                val file = File(it.toString())
                file.name.startsWith(prefix) && file.name.endsWith(".xml")
            }
            .collect(Collectors.toList())

        if (inputList.size == 0) {
            fail("No $prefix messages were tested")
        }

        inputList.forEach { inputFile ->
            println("Testing file $inputFile")

            val message = IParser.DefaultParser().parseXML(inputFile)
            measureTest(
                iterations = 1,
                testCycles = 1,
                warmUpCycles = 0,
                printPrefix = "[TestAllXmls] ${message.payloadClass}"
            ) {
                val outputFilePath = Paths.get(tempDir.toString(), "${message.payloadClass.simpleName}.pdf")

                val reporter = Reporter(
                    resourceBasePath = resourcesDir
                )

                reporter.buildReport(
                    message = message,
                    outputFilePath = outputFilePath,
                    internalData = InternalData("Internal Id", "internal Status")
                )
            }

        }

    }
}