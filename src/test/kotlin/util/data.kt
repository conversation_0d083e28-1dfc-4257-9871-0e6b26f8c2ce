import com.sun.org.apache.xerces.internal.jaxp.datatype.XMLGregorianCalendarImpl
import gr.datamation.mx.Message
import gr.datamation.mx.message.pacs.PaymentReturn04
import xsd.pacs_004_001_04.*
import java.math.BigDecimal
import java.util.*
import javax.xml.datatype.DatatypeConstants

object PaymentReturnTest {
    fun createMessage(): Message<*, *, *> {
        val gc = GregorianCalendar()
        val todayDateTime = XMLGregorianCalendarImpl.createDateTime(
            gc[Calendar.YEAR], gc[Calendar.MONTH] + 1,
            gc[Calendar.DAY_OF_MONTH], gc[Calendar.HOUR], gc[Calendar.MINUTE], gc[Calendar.SECOND]
        )
        val msg = PaymentReturn04()
        val paymentReturnV04 = PaymentReturnV04()
        val groupHeader54 = GroupHeader54()
        groupHeader54.msgId = "CCCC/121122-PR007"
        groupHeader54.creDtTm = todayDateTime
        groupHeader54.nbOfTxs = "1"
        val settlementInstruction4 = SettlementInstruction1()
        settlementInstruction4.sttlmMtd = SettlementMethod1Code.CLRG
        val clearingSystemIdentification3Choice = ClearingSystemIdentification3Choice()
        clearingSystemIdentification3Choice.prtry = "CBJ"
        settlementInstruction4.clrSys = clearingSystemIdentification3Choice
        groupHeader54.sttlmInf = settlementInstruction4
        var branchAndFinancialInstitutionIdentification5 = BranchAndFinancialInstitutionIdentification5()
        var financialInstitutionIdentification8 = FinancialInstitutionIdentification8()
        financialInstitutionIdentification8.bicfi = "CCCCIE2D"
        branchAndFinancialInstitutionIdentification5.finInstnId = financialInstitutionIdentification8
        groupHeader54.instgAgt = branchAndFinancialInstitutionIdentification5
        branchAndFinancialInstitutionIdentification5 = BranchAndFinancialInstitutionIdentification5()
        financialInstitutionIdentification8 = FinancialInstitutionIdentification8()
        financialInstitutionIdentification8.bicfi = "BBBBIE2D"
        branchAndFinancialInstitutionIdentification5.finInstnId = financialInstitutionIdentification8
        groupHeader54.instdAgt = branchAndFinancialInstitutionIdentification5
        paymentReturnV04.grpHdr = groupHeader54
        val originalGroupHeader2 = OriginalGroupHeader2()
        originalGroupHeader2.orgnlMsgId = "BBBB/121109-CBJ056"
        originalGroupHeader2.orgnlMsgNmId = "pacs.008.001.03"
        originalGroupHeader2.orgnlCreDtTm = todayDateTime
        paymentReturnV04.orgnlGrpInf = originalGroupHeader2
        val paymentTransaction44 = PaymentTransaction44()
        paymentTransaction44.rtrId = "CCCC/121122-PR007"
        paymentTransaction44.orgnlInstrId = "BBBB/121109-CBJ056/1"
        paymentTransaction44.orgnlEndToEndId = "CROPS/SX-25T/2012-10-13"
        paymentTransaction44.orgnlTxId = "AAAA/121109-CCT/EUR443/1"
        var activeOrHistoricCurrencyAndAmount = ActiveOrHistoricCurrencyAndAmount()
        activeOrHistoricCurrencyAndAmount.ccy = "EUR"
        activeOrHistoricCurrencyAndAmount.value = BigDecimal("74900")
        paymentTransaction44.orgnlIntrBkSttlmAmt = activeOrHistoricCurrencyAndAmount
        val activeCurrencyAndAmount = ActiveCurrencyAndAmount()
        activeCurrencyAndAmount.ccy = "EUR"
        activeCurrencyAndAmount.value = BigDecimal("74750")
        paymentTransaction44.rtrdIntrBkSttlmAmt = activeCurrencyAndAmount
        paymentTransaction44.intrBkSttlmDt =
            XMLGregorianCalendarImpl.createDate(2012, 11, 22, DatatypeConstants.FIELD_UNDEFINED)
        activeOrHistoricCurrencyAndAmount = ActiveOrHistoricCurrencyAndAmount()
        activeOrHistoricCurrencyAndAmount.ccy = "EUR"
        activeOrHistoricCurrencyAndAmount.value = BigDecimal("74850")
        paymentTransaction44.rtrdInstdAmt = activeOrHistoricCurrencyAndAmount
        paymentTransaction44.chrgBr = ChargeBearerType1Code.CRED
        val charges2 = Charges2()
        activeOrHistoricCurrencyAndAmount = ActiveOrHistoricCurrencyAndAmount()
        activeOrHistoricCurrencyAndAmount.ccy = "EUR"
        activeOrHistoricCurrencyAndAmount.value = BigDecimal("100")
        charges2.amt = activeOrHistoricCurrencyAndAmount
        branchAndFinancialInstitutionIdentification5 = BranchAndFinancialInstitutionIdentification5()
        financialInstitutionIdentification8 = FinancialInstitutionIdentification8()
        financialInstitutionIdentification8.bicfi = "CCCCIE2D"
        branchAndFinancialInstitutionIdentification5.finInstnId = financialInstitutionIdentification8
        charges2.agt = branchAndFinancialInstitutionIdentification5
        paymentTransaction44.chrgsInf.add(charges2)
        val paymentReturnReason1 = PaymentReturnReason1()
        val partyIdentification43 = PartyIdentification43()
        partyIdentification43.nm = "Seed Inc."
        paymentReturnReason1.orgtr = partyIdentification43
        val returnReason5Choice = ReturnReason5Choice()
        returnReason5Choice.cd = "NARR"
        paymentReturnReason1.rsn = returnReason5Choice
        paymentReturnReason1.addtlInf.add("RETURN AFTER ACCEPTED PAYMENT CANCELLATION REQUEST")
        paymentTransaction44.rtrRsnInf.add(paymentReturnReason1)
        paymentReturnV04.txInf.add(paymentTransaction44)
        msg.message = paymentReturnV04
        return msg
    }
}