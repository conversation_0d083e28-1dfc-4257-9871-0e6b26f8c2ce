package util

/**
 * Iterates provided by [callback] code [iterations]x[testCycles] times.
 * Performs warming by iterating [iterations]x[warmUpCycles] times.
 */
fun measureTest(
    iterations: Int = 1000,
    testCycles: Int = 10,
    warmUpCycles: Int = 2,
    printPrefix: String = "[MeasureTest]",
    callback: () -> Unit
) {
    val results = ArrayList<Long>()
    var totalTime = 0L
    var t = 0

    println("$printPrefix -> go")

    while (++t <= testCycles + warmUpCycles) {
        val startTime = System.currentTimeMillis()

        var i = 0
        while (i++ < iterations)
            callback()

        if (t <= warmUpCycles) {
            println("$printPrefix Warming $t of $warmUpCycles")
            continue
        }

        val time = System.currentTimeMillis() - startTime
        println(printPrefix + " " + time.toString() + "ms")

        results.add(time)
        totalTime += time
    }

    results.sort()

    val average = totalTime / testCycles
    val median = results[results.size / 2]

    println("$printPrefix -> average=${average}ms / median=${median}ms")
}