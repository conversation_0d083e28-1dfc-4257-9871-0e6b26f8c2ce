package gr.datamation.mx.report.creator

import gr.datamation.mx.report.layout.DefaultLayout
import gr.datamation.mx.report.model.InternalData
import gr.datamation.mx.report.model.LabelAndValue
import gr.datamation.mx.report.model.Pacs008Msg
import org.celtric.kotlin.html.Node
import org.celtric.kotlin.html.h2

// TODO localise texts
class Pacs008Report(
    resourceBasePath: String,
    internalData: InternalData,
    externalData: Map<String, String>,
) : ReportCreator<Pacs008Msg>(
    "FI to FI Customer Credit Transfer",
    DefaultLayout(resourceBasePath),
    internalData,
    externalData
) {
    override fun getHtmlPages(data: Pacs008Msg): List<List<Node>> {
        return data.transactions.map { transaction ->
            listOf(
                msgInfoElement(
                    messageName(data.msgType, data.majorVersion, data.minorVersion),
                    data.msgInfo,
                    internalData,
                ),
                externalInfoElement(externalData),
                h2 { "Credit Transfer Information" },
                createTable(
                    listOf(
                        LabelAndValue("Transaction Id", transaction.transactionId),
                        LabelAndValue("Instruction ID", transaction.instructionId),
                        LabelAndValue("End To End ID", transaction.endToEndId),
                        LabelAndValue("Interbank Settlement Date", transaction.interbankSettlementDate),
                        LabelAndValue("Interbank Settlement Amount", transaction.interbankSettlementAmount),
                        LabelAndValue("Instructed Amount", transaction.instructedAmount),
                        LabelAndValue("Exchange Rate", transaction.exchangeRate),
                        LabelAndValue("Charge Bearer", transaction.chargeBearer),
                        LabelAndValue("Charges Information", transaction.chargesInformation),
                        LabelAndValue("Purpose of the Message", transaction.purposeOfTheMessage),
                        LabelAndValue("Internal Purpose of the Message", transaction.internalPurposeOfTheMessage),
                        LabelAndValue("1st Intermediary Agent Name", transaction.firstIntermediaryAgentName),
                        LabelAndValue("1st Intermediary Agent ID", transaction.firstIntermediaryAgentId),
                        LabelAndValue("2nd Intermediary Agent Name", transaction.secondIntermediaryAgentName),
                        LabelAndValue("2nd Intermediary Agent ID", transaction.secondIntermediaryAgentId),
                        LabelAndValue("3rd Intermediary Agent Name", transaction.thirdIntermediaryAgentName),
                        LabelAndValue("3rd Intermediary Agent ID", transaction.thirdIntermediaryAgentId),
                        LabelAndValue("Remittance Information", transaction.remittanceInformation),
                    ),
                ),
                debtorCreditorBlock(debtor = transaction.debtor, creditor = transaction.creditor)
            )
        }
    }
}