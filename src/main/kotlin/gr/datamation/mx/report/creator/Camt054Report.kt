package gr.datamation.mx.report.creator

import gr.datamation.mx.report.layout.DefaultLayout
import gr.datamation.mx.report.model.Camt054Msg
import gr.datamation.mx.report.model.InternalData
import gr.datamation.mx.report.model.LabelAndValue
import gr.datamation.mx.report.model.Row
import org.celtric.kotlin.html.Node
import org.celtric.kotlin.html.h2

// TODO localise texts
class Camt054Report(
    resourceBasePath: String,
    internalData: InternalData,
    externalData: Map<String, String>,
    ) : ReportCreator<Camt054Msg>(
    "Bank to Customer Debit / Credit Notification",
    DefaultLayout(resourceBasePath),
    internalData,
    externalData
) {
    override fun getHtmlPages(data: Camt054Msg): List<List<Node>> {
        return data.notifications.map { statement ->
            listOf(
                msgInfoElement(
                    messageName(data.msgType, data.majorVersion, data.minorVersion),
                    data.msgInfo,
                    internalData,
                ),
                externalInfoElement(externalData),
                h2 { "Notification Information" },
                createTwoColTable(
                    listOf(
                        Row(
                            LabelAndValue("Created on", statement.createdOn),
                            LabelAndValue("Booking Date", statement.bookingDate),
                        ),
                        Row(
                            LabelAndValue("Account", statement.account),
                            LabelAndValue("Value Date", statement.valueDate),
                        ),
                    ),
                ),
                h2 { "Transaction details" },
                createTable(
                    listOf(
                        "Entry Reference",
                        "Transaction ID",
                        "Amount",
                        "Credit/Debit Indicator",
                        "Status",
                        "Purpose",
                        "Transaction Information"
                    ),
                    statement.transactions.map { transaction ->
                        listOf(
                            transaction.entryRef,
                            transaction.transactionId,
                            transaction.amount,
                            transaction.creditDebitIndicator,
                            transaction.status,
                            transaction.purpose,
                            transaction.transactionInfo
                        )
                    }
                )
            )
        }
    }
}