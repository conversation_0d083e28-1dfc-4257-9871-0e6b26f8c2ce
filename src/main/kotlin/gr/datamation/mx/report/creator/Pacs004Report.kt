package gr.datamation.mx.report.creator

import gr.datamation.mx.report.layout.DefaultLayout
import gr.datamation.mx.report.model.InternalData
import gr.datamation.mx.report.model.LabelAndValue
import gr.datamation.mx.report.model.Pacs004Msg
import org.celtric.kotlin.html.Node
import org.celtric.kotlin.html.h2

// TODO localise texts
class Pacs004Report(
    resourceBasePath: String,
    internalData: InternalData,
    externalData: Map<String, String>,
) : ReportCreator<Pacs004Msg>(
    "Payment Return",
    DefaultLayout(resourceBasePath),
    internalData,
    externalData,
) {
    override fun getHtmlPages(data: Pacs004Msg): List<List<Node>> {
        return data.transactions.map { transaction ->
            listOf(
                msgInfoElement(
                    messageName(data.msgType, data.majorVersion, data.minorVersion),
                    data.msgInfo,
                    internalData,
                ),
                externalInfoElement(externalData),
                h2 { "Payment Return Information" },
                createTable(
                    listOf(
                        LabelAndValue("Original Message ID", transaction.originalMessageId),
                        LabelAndValue(
                            "Original Interbank Settlement Amount",
                            transaction.originalInterbankSettlementAmount
                        ),
                        LabelAndValue(
                            "Returned Interbank Settlement Amount",
                            transaction.returnedInterbankSettlementAmount
                        ),
                        LabelAndValue("Original Transaction ID", transaction.originalTransactionId),
                        LabelAndValue("Original Instruction ID", transaction.originalInstructionId),
                        LabelAndValue("Original End To End ID", transaction.originalEndToEndId),
                        LabelAndValue("Return ID", transaction.returnId),
                        LabelAndValue("Return Reason", transaction.returnReason),
                    ),
                ),
                debtorCreditorBlock(debtor = transaction.debtor, creditor = transaction.creditor)
            )
        }
    }
}