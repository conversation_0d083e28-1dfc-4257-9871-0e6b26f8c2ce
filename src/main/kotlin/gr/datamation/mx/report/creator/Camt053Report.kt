package gr.datamation.mx.report.creator

import gr.datamation.mx.report.layout.DefaultLayout
import gr.datamation.mx.report.model.Camt053Msg
import gr.datamation.mx.report.model.InternalData
import gr.datamation.mx.report.model.LabelAndValue
import gr.datamation.mx.report.model.Row
import org.celtric.kotlin.html.Node
import org.celtric.kotlin.html.h2

// TODO localise texts
class Camt053Report(
    resourceBasePath: String,
    internalData: InternalData,
    externalData: Map<String, String>,
) : ReportCreator<Camt053Msg>(
    "Bank to Customer Statement",
    DefaultLayout(resourceBasePath),
    internalData,
    externalData,
) {
    override fun getHtmlPages(data: Camt053Msg): List<List<Node>> {
        return data.statements.map { statement ->
            listOf(
                msgInfoElement(
                    messageName(data.msgType, data.majorVersion, data.minorVersion),
                    data.msgInfo,
                    internalData,
                ),
                externalInfoElement(externalData),
                h2 { "Statement Information" },
                createTwoColTable(
                    listOf(
                        Row(
                            LabelAndValue("Created on", statement.createdOn),
                            LabelAndValue("From", statement.from),
                        ),
                        Row(
                            LabelAndValue("Account", statement.account),
                            LabelAndValue("To", statement.to),
                        ),
                        Row(
                            LabelAndValue("Opening Balance", statement.openingBalance),
                            LabelAndValue("Closing Balance", statement.closingBalance),
                        ),
                        Row(
                            LabelAndValue(
                                "Available Opening Balance",
                                statement.availableOpeningBalance
                            ),
                            LabelAndValue(
                                "Available Closing Balance",
                                statement.availableClosingBalance
                            ),
                        )
                    ),
                ),
                h2 { "Transaction details" },
                createTable(
                    listOf(
                        "Entry Reference",
                        "Transaction ID",
                        "Transaction Creation Date",
                        "Amount",
                        "Credit/Debit Indicator",
                        "Transaction Information"
                    ),
                    statement.transactions.map { transaction ->
                        listOf(
                            transaction.entryRef,
                            transaction.transactionId,
                            transaction.transactionCreationDate,
                            transaction.amount,
                            transaction.creditDebitIndicator,
                            transaction.transactionInfo
                        )
                    }
                )
            )
        }
    }
}