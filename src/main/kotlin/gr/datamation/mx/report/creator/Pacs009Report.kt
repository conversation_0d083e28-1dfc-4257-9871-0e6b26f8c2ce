package gr.datamation.mx.report.creator

import gr.datamation.mx.report.layout.DefaultLayout
import gr.datamation.mx.report.model.InternalData
import gr.datamation.mx.report.model.LabelAndValue
import gr.datamation.mx.report.model.Pacs009Msg
import org.celtric.kotlin.html.Node
import org.celtric.kotlin.html.h2

// TODO localise texts
class Pacs009Report(
    resourceBasePath: String,
    internalData: InternalData,
    externalData: Map<String, String>,
) : ReportCreator<Pacs009Msg>(
    "Financial Institution Credit Transfer",
    DefaultLayout(resourceBasePath),
    internalData,
    externalData
) {

    override fun getHtmlPages(data: Pacs009Msg): List<List<Node>> {
        return data.transactions.map { transaction ->
            listOf(
                msgInfoElement(
                    messageName(data.msgType, data.majorVersion, data.minorVersion),
                    data.msgInfo,
                    internalData,
                ),
                externalInfoElement(externalData),
                h2 { "Credit Transfer Information" },
                createTable(
                    listOf(
                        LabelAndValue("Transaction Id", transaction.transactionId),
                        LabelAndValue("Instruction ID", transaction.instructionId),
                        LabelAndValue("End To End ID", transaction.endToEndId),
                        LabelAndValue("Interbank Settlement Debit Date", transaction.interbankSettlementDebitDate),
                        LabelAndValue(
                            "Interbank Settlement Credit Date",
                            transaction.interbankSettlementCreditDate
                        ),
                        LabelAndValue(
                            "Interbank Settlement Amount",
                            transaction.interbankSettlementAmount
                        ),
                        LabelAndValue("Purpose of the Message", transaction.purposeOfTheMessage),
                        LabelAndValue("Internal Purpose of the Message", transaction.internalPurposeOfTheMessage),
                        LabelAndValue("1st Intermediary Agent Name", transaction.firstIntermediaryAgentName),
                        LabelAndValue("1st Intermediary Agent ID", transaction.firstIntermediaryAgentId),
                        LabelAndValue("2nd Intermediary Agent Name", transaction.secondIntermediaryAgentName),
                        LabelAndValue("2nd Intermediary Agent ID", transaction.secondIntermediaryAgentId),
                        LabelAndValue("3rd Intermediary Agent Name", transaction.thirdIntermediaryAgentName),
                        LabelAndValue("3rd Intermediary Agent ID", transaction.thirdIntermediaryAgentId),
                    ),
                ),
                debtorCreditorBlock(debtor = transaction.debtor, creditor = transaction.creditor)
            )
        }
    }
}