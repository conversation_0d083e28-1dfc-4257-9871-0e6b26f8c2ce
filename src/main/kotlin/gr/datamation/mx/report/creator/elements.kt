package gr.datamation.mx.report.creator

import com.itextpdf.html2pdf.ConverterProperties
import com.itextpdf.html2pdf.HtmlConverter
import com.itextpdf.layout.RootElement
import com.itextpdf.layout.element.IBlockElement
import gr.datamation.mx.report.mapping.NO_AMOUNT
import gr.datamation.mx.report.mapping.NO_VALUE
import gr.datamation.mx.report.model.*
import org.celtric.kotlin.html.*
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.*
import javax.xml.datatype.XMLGregorianCalendar

fun messageName(msgType: String, majorVersion: String, minorVersion: String): String {
    return "${msgType}.${majorVersion}.${minorVersion}"
}

fun formatDate(date: Date?): String {
    return when (date) {
        null -> NO_VALUE
        // TODO parameterise the format
        else -> SimpleDateFormat("dd/MM/yyyy").format(date)
    }
}

fun formatTime(date: Date?): String {
    return when (date) {
        null -> NO_VALUE
        // TODO parameterise the format
        else -> SimpleDateFormat("HH:mm").format(date)
    }
}

fun formatDate(xCal: XMLGregorianCalendar?): String {
    return when (xCal) {
        null -> NO_VALUE
        else -> formatDate(xCal.toGregorianCalendar().time)
    }
}

fun formatMoney(amount: BigDecimal?, currency: String?): String {
    return when (amount) {
        null -> NO_AMOUNT
        else -> "$amount $currency"
    }
}

fun formatAddress(
    buildingNumber: String?,
    streetName: String?,
    town: String?,
    postalCode: String?,
    orElse: () -> String? = { NO_VALUE }
): String {
    val isAtLeastOnePropertyDefined = listOf(buildingNumber, streetName, town, postalCode).any { !it.isNullOrBlank() }
    return when (isAtLeastOnePropertyDefined) {
        true -> "$buildingNumber $streetName, $town $postalCode"
        false -> orElse() ?: NO_VALUE
    }
}

fun styledHtml(body: () -> Any): Node {
    return html {
        head {
            link(rel = "stylesheet", type = "text/css", href = "css/style.css")
        } + body(content = body)
    }
}

fun RootElement<*>.addHtml(resourceBasePath: String, content: () -> Node) {
    val contentHtml = styledHtml { content() }.render()

    return this.addHtml(contentHtml, resourceBasePath)
}

fun RootElement<*>.addHtml(html: String, resourceBasePath: String) {
    val props: ConverterProperties = ConverterProperties()
        .setBaseUri(resourceBasePath) // TODO switch here to allow local resource resolution

    val elements = HtmlConverter.convertToElements(html, props)

    elements.forEach {
        val element = it as IBlockElement
        this.add(element)
    }
}

private fun <IT> stripedTable(data: List<IT>, cells: (Int, IT) -> List<Node>): List<Node> {
    return data.mapIndexed { index, entry ->
        val classes = when {
            index % 2 == 1 -> "even"
            else -> "odd"
        }
        tr(classes = classes) {
            cells(index, entry)
        }
    }
}

private fun <IT> stripedWrappedTable(data: List<IT>, cells: (Int, IT) -> List<Node>): List<Node> {
    val left = data.subList(0, (data.size / 2) + 1) // longer list to handle the case of odd number of elements
    val right = data.subList((data.size / 2) + 1, data.size)

    return stripedTable(left) { index, _ ->
        cells(index, left[index]) + if (right.size > index) {
            cells(index, right[index])
        } else {
            listOf(td {})
        }
    }
}

fun createTable(data: List<LabelAndValue>, wrapAfter: Int = 10): Node {
    return table(classes = "with-border") {
        tbody {
            when {
                data.size > wrapAfter -> stripedWrappedTable(data) { _, entry ->
                    listOf(
                        td(classes = "label") {
                            entry.label
                        }, td {
                            entry.value ?: NO_VALUE
                        }
                    )
                }
                else -> stripedTable(data) { _, entry ->
                    listOf(
                        td(classes = "label") {
                            entry.label
                        }, td {
                            entry.value ?: NO_VALUE
                        }
                    )
                }

            }
        }
    }
}

fun createTwoColTable(data: List<Row>): Node {
    return table(classes = "with-border with-offset") {
        tbody {
            data.map { row ->
                tr {
                    listOf(
                        td(classes = "label") {
                            row.left.label
                        }, td {
                            row.left.value ?: NO_VALUE
                        },
                        td(classes = "label") {
                            row.right?.label ?: ""
                        },
                        td {
                            row.right?.value ?: ""
                        })
                }
            }
        }
    }
}

fun createTable(headers: List<String>, content: List<List<String>>): Node {
    return table {
        thead {
            tr {
                headers.map {
                    th { it }
                }
            }
        } + tbody {
            stripedTable(content) { _, row ->
                row.map { cell ->
                    td { cell }
                }
            }
        }
    }
}

fun agentInfoElement(agent: AgentInfo): Node {
    return createTable(
        listOf(
            LabelAndValue("Name", agent.name),
            LabelAndValue("IBAN", agent.iban),
            LabelAndValue("BICFI", agent.bicfi),
            LabelAndValue("Address", agent.address),
            LabelAndValue("Country", agent.country),
        ),
    )
}

fun debtorCreditorBlock(debtor: AgentInfo, creditor: AgentInfo): Node {
    return div(classes = "row-div") {
        div(classes = "left-col") {
            h2 { "Debtor Info" } + agentInfoElement(debtor)
        } + div(classes = "right-col") {
            h2 { "Creditor Info" } + agentInfoElement(creditor)
        }
    }
}

fun msgInfoElement(messageName: String, msgInfo: MsgInfo, internalData: InternalData): Node {
    return createTwoColTable(
        listOf(
            Row(
                LabelAndValue("Message Name", messageName),
                LabelAndValue("Message ID", msgInfo.messageId)
            ),
            Row(
                LabelAndValue("Internal ID", internalData.id),
                LabelAndValue("Internal status", internalData.status),
            ),
            Row(
                LabelAndValue("Creation Date", msgInfo.creationDate),
                null
            )
        )
    )
}

fun externalInfoElement(externalData: Map<String, String>): Node {
    if (externalData.isEmpty())
        return div {}

    return div {
        h2 { "External info" } +
        createTable(
            externalData.map { (key, value) -> LabelAndValue(key, value as String?) }
        )
    }
}