package gr.datamation.mx.report.creator

import org.apache.pdfbox.pdmodel.PDDocument
import org.apache.pdfbox.pdmodel.PDDocumentInformation
import org.apache.pdfbox.pdmodel.PDPage
import org.apache.pdfbox.pdmodel.common.PDRectangle
import gr.datamation.mx.report.layout.DefaultLayout
import gr.datamation.mx.report.model.InternalData
import org.celtric.kotlin.html.Node
import org.celtric.kotlin.html.h1
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.util.*

interface IReportCreator<M> {
    fun create(outputFile: File, msg: M)

    fun createByteArray(msg: M): ByteArray
}

abstract class ReportCreator<M>(
    protected val title: String,
    private val layout: DefaultLayout,
    protected val internalData: InternalData,
    protected val externalData: Map<String, String>,
) : IReportCreator<M> {
    private fun addMetadata(pdfDoc: PDDocument) {
        val pdfMetaData = PDDocumentInformation()
        pdfMetaData.creationDate = Calendar.getInstance()
        pdfMetaData.author = "PaymentComponents"
        pdfMetaData.producer = "aplonReporter"
        pdfMetaData.creator = "aplonReporter"
        pdfMetaData.subject = "MX message report"
        pdfDoc.documentInformation = pdfMetaData
    }

    private fun createPdfDocument(): PDDocument {
        val pdfDoc = PDDocument()
        addMetadata(pdfDoc)

        return pdfDoc
    }

    override fun create(outputFile: File, msg: M) {
        val pdfDoc = createPdfDocument()

        fillLayout(pdfDoc, getHtmlPages(msg))

        FileOutputStream(outputFile).use { outputStream ->
            pdfDoc.save(outputStream)
        }

        pdfDoc.close()
    }

    override fun createByteArray(msg: M): ByteArray {
        val pdfDoc = createPdfDocument()
        val outputStream = ByteArrayOutputStream()

        fillLayout(pdfDoc, getHtmlPages(msg))

        pdfDoc.save(outputStream)
        pdfDoc.close()
        return outputStream.toByteArray()
    }

    private fun fillLayout(document: PDDocument, pages: List<List<Node>>) {
        layout.withDocument(document) {
            pages.mapIndexed { index, page ->
                val pageElements = page.toMutableList()

                when (index) {
                    0 -> {
                        pageElements.add(0, h1 { title })
                    }
                    else -> {
                        // start a new doc page for every virtual page
                        document.addPage(PDPage(PDRectangle.A4))
                    }
                }

                val htmlPageCode = styledHtml { pageElements }.render()

                layout.addHtmlToDocument(document, htmlPageCode)
            }
        }
    }

    abstract fun getHtmlPages(data: M): List<List<Node>>
}