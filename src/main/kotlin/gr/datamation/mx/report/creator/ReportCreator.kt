package gr.datamation.mx.report.creator

import com.itextpdf.kernel.pdf.PdfDocument
import com.itextpdf.kernel.pdf.PdfDocumentInfo
import com.itextpdf.kernel.pdf.PdfWriter
import com.itextpdf.layout.element.AreaBreak
import com.itextpdf.layout.properties.AreaBreakType
import gr.datamation.mx.report.layout.DefaultLayout
import gr.datamation.mx.report.model.InternalData
import org.celtric.kotlin.html.Node
import org.celtric.kotlin.html.h1
import java.io.ByteArrayOutputStream
import java.io.File

interface IReportCreator<M> {
    fun create(outputFile: File, msg: M)

    fun createByteArray(msg: M): ByteArray
}

abstract class ReportCreator<M>(
    protected val title: String,
    private val layout: DefaultLayout,
    protected val internalData: InternalData,
    protected val externalData: Map<String, String>,
) : IReportCreator<M> {
    private fun addMetadata(pdfDoc: PdfDocument) {
        val pdfMetaData: PdfDocumentInfo = pdfDoc.documentInfo
        pdfMetaData.addCreationDate()
        pdfMetaData.author = "PaymentComponents"
        pdfMetaData.producer = "aplonReporter"
        pdfMetaData.creator = "aplonReporter"
        pdfMetaData.subject = "MX message report"
    }

    private fun createPdfDocument(outputFile: File): PdfDocument {
        val writer = PdfWriter(outputFile)
        val pdfDoc = PdfDocument(writer)

        addMetadata(pdfDoc)

        return pdfDoc
    }

    private fun createPdfDocument(outputStream: ByteArrayOutputStream): PdfDocument {
        val writer = PdfWriter(outputStream)
        val pdfDoc = PdfDocument(writer)

        addMetadata(pdfDoc)

        return pdfDoc
    }

    override fun create(outputFile: File, msg: M) {
        val pdfDoc = createPdfDocument(outputFile)

        fillLayout(pdfDoc, getHtmlPages(msg))

        pdfDoc.close()
    }

    override fun createByteArray(msg: M): ByteArray {
        val outputStream = ByteArrayOutputStream()
        val pdfDoc = createPdfDocument(outputStream)

        fillLayout(pdfDoc, getHtmlPages(msg))

        pdfDoc.close()
        return outputStream.toByteArray()
    }

    private fun fillLayout(document: PdfDocument, pages: List<List<Node>>) {
        layout.withDocument(document) { doc ->
            pages.mapIndexed { index, page ->
                val pageElements = page.toMutableList()

                when (index) {
                    0 -> {
                        pageElements.add(0, h1 { title })
                    }
                    else -> {
                        // start a new doc page for every virtual page
                        doc.add(AreaBreak(AreaBreakType.NEXT_PAGE))
                    }
                }

                val htmlPageCode = styledHtml { pageElements }.render()

                doc.addHtml(htmlPageCode, layout.resourceBasePath)

                doc.flush()
            }
        }
    }

    abstract fun getHtmlPages(data: M): List<List<Node>>
}