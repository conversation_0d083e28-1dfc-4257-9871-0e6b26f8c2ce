package gr.datamation.mx.report

import com.itextpdf.kernel.pdf.PdfDocument
import gr.datamation.mx.Message
import gr.datamation.mx.report.model.InternalData
import java.io.File
import java.nio.file.Path
import java.nio.file.Paths

class Reporter {
    private val parser: IParser
    private val dispatcher: IDispatcher
    private val resourceBasePath: Path

    // TODO allow to use internal html resources from the jar
    constructor() {
        parser = IParser.DefaultParser()
        dispatcher = Dispatcher.DefaultDispatcher()
        resourceBasePath = Paths.get("./src/main/resources") // TODO parameterise the value
    }

    /**
     * @param resourceBasePath Path to the resource dir. This value will be passed to the html-to-pdf converter to resolve resources referenced in the report body.
     */
    constructor(resourceBasePath: Path) {
        parser = IParser.DefaultParser()
        dispatcher = Dispatcher.DefaultDispatcher()
        this.resourceBasePath = resourceBasePath
    }

    /**
     * @param parser
     * @param dispatcher
     * @param resourceBasePath Path to the resource dir. This value will be passed to the html-to-pdf converter to resolve resources referenced in the report body.
     */
    internal constructor(
        parser: IParser,
        dispatcher: IDispatcher,
        resourceBasePath: Path
    ) {
        this.parser = parser
        this.dispatcher = dispatcher
        this.resourceBasePath = resourceBasePath
    }

    /**
     * Given an input and output file paths, converts the input file into an internal object and transforms it into a PDF report.
     * The resulting PDF is stored at the specified output file path.
     * @param inputFilePath Path to the input XML file. Path can be absolute or relative within the ClassLoader.
     * @param outputFilePath Path to the output PDF file. Path can be absolute or relative to the working directory.
     */
    fun buildReport(
        inputFilePath: Path,
        outputFilePath: Path,
        internalData: InternalData,
        externalData: Map<String, String> = emptyMap()
    ) {
        val parsed = parser.parseXML(inputFilePath)
        buildReport(
            message = parsed,
            outputFilePath = outputFilePath,
            internalData = internalData,
            externalData = externalData
        )
    }

    /**
     * Given an input and output file paths, converts the input message text into an internal object and transforms it into a PDF report.
     * The resulting PDF is stored at the specified output file path.
     * @param messageText Input XML text.
     * @param outputFilePath Path to the output PDF file. Path can be absolute or relative to the working directory.
     */
    fun buildReport(
        messageText: String,
        outputFilePath: Path,
        internalData: InternalData,
        externalData: Map<String, String> = emptyMap()
    ) {
        val parsed = parser.parseXML(messageText)
        buildReport(
            message = parsed,
            outputFilePath = outputFilePath,
            internalData = internalData,
            externalData = externalData
        )
    }

    /**
     * Given a message and an output file path, converts the message into a PDF report.
     * The resources base path should include the styles and images for the PDF layout.
     * The resulting PDF is stored at the specified output file path.
     * @param message Swift MX message. This object should come from the PaymentComponents library.
     * @param outputFilePath Path to the output PDF file. Path can be absolute or relative to the working directory.
     */
    fun buildReport(
        message: Message<*, *, *>,
        outputFilePath: Path,
        internalData: InternalData,
        externalData: Map<String, String> = emptyMap()
    ) {
        val outputFile = File(outputFilePath.toString())
        dispatcher.createReport(
            message = message.message,
            outputFile = outputFile,
            resourceBasePath = resourceBasePath.toString(),
            internalData = internalData,
            externalData = externalData
        )
    }

    /**
     * Given a message, converts the message into ByteArray.
     * The resources base path should include the styles and images for the PDF layout.
     * @param message Swift MX message. This object should come from the PaymentComponents library.
     */
    fun buildReport(
        message: Message<*, *, *>,
        internalData: InternalData,
        externalData: Map<String, String> = emptyMap()
    ): ByteArray = dispatcher.createByteArrayReport(
            message = message.message,
            resourceBasePath = resourceBasePath.toString(),
            internalData = internalData,
            externalData = externalData
        )
}
