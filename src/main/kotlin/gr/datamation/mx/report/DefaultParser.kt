package gr.datamation.mx.report

import gr.datamation.mx.MXUtils
import gr.datamation.mx.Message
import java.io.IOException
import java.nio.file.Files
import java.nio.file.Path
import jakarta.xml.bind.JAXBException
import javax.xml.stream.XMLStreamException

interface IParser {
    fun parseXML(path: Path): Message<*, *, *>

    fun parseXML(text: String): Message<*, *, *> = MXUtils.autoParseXML(text)

    class DefaultParser : IParser {
        @Throws(IOException::class)
        private fun getFileContents(path: Path): String {
            val reader = Files.newBufferedReader(path)
                ?: throw IOException("Illegal path - did not resolve: $path")

            return reader.readText()
        }

        @Throws(
            IllegalAccessException::class,
            InstantiationException::class,
            JAXBException::class,
            XMLStreamException::class,
            IOException::class
        )
        override fun parseXML(path: Path): Message<*, *, *> {
            val fileContent = getFileContents(path)
            return MXUtils.autoParseXML(fileContent)
        }
    }
}

