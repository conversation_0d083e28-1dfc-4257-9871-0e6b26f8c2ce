package gr.datamation.mx.report.model

import gr.datamation.mx.report.mapping.NO_VALUE

data class InternalData(val id: String?, val status: String?)

data class MsgInfo(
    val creationDate: String,
    val messageId: String,
)

data class AgentInfo(
    val name: String? = NO_VALUE,
    val iban: String? = NO_VALUE,
    val bicfi: String? = NO_VALUE,
    val address: String? = NO_VALUE,
    val country: String? = NO_VALUE,
)

data class LabelAndValue(val label: String, val value: String?)

data class Row(val left: LabelAndValue, val right: LabelAndValue?)