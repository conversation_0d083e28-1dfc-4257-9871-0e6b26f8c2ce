package gr.datamation.mx.report.model

data class Pacs008Msg(
    val msgInfo: MsgInfo,
    val transactions: List<Pacs008Transaction>,
    val minorVersion: String
) {
    val msgType = "pacs.008"
    val majorVersion = "001"
}

data class Pacs008Transaction(
    val transactionId: String,
    val instructionId: String,
    val endToEndId: String,
    val interbankSettlementDate: String,
    val interbankSettlementAmount: String,
    val instructedAmount: String,
    val exchangeRate: String,
    val chargeBearer: String,
    val chargesInformation: String,
    val purposeOfTheMessage: String,
    val internalPurposeOfTheMessage: String,
    val firstIntermediaryAgentName: String,
    val firstIntermediaryAgentId: String,
    val secondIntermediaryAgentName: String,
    val secondIntermediaryAgentId: String,
    val thirdIntermediaryAgentName: String,
    val thirdIntermediaryAgentId: String,
    val debtor: AgentInfo,
    val creditor: AgentInfo,
    val remittanceInformation: String,
)