package gr.datamation.mx.report.model

data class Camt053Msg(
    val msgInfo: MsgInfo,
    val statements: List<Camt053Statement>,
    val minorVersion: String
) {
    val msgType = "camt.053"
    val majorVersion = "001"
}

data class Camt053Statement(
    val createdOn: String,
    val openingBalance: String,
    val closingBalance: String,
    val availableOpeningBalance: String,
    val availableClosingBalance: String,
    val account: String,
    val from: String,
    val to: String,
    val transactions: List<Camt053Transaction>
)

data class Camt053Transaction(
    val entryRef: String,
    val transactionId: String,
    val transactionCreationDate: String,
    val amount: String,
    val creditDebitIndicator: String,
    val transactionInfo: String
)