package gr.datamation.mx.report.model

data class Pacs004Msg(
    val msgInfo: MsgInfo,
    val transactions: List<Pacs004Transaction>,
    val minorVersion: String
) {
    val msgType = "pacs.004"
    val majorVersion = "001"
}

data class Pacs004Transaction(
    val originalMessageId: String,
    val originalInterbankSettlementAmount: String,
    val returnedInterbankSettlementAmount: String,
    val originalTransactionId: String,
    val originalInstructionId: String,
    val originalEndToEndId: String,
    val returnId: String,
    val returnReason: String,
    val debtor: AgentInfo,
    val creditor: AgentInfo,
)