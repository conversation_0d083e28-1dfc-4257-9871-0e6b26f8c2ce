package gr.datamation.mx.report.model

data class Camt054Msg(
    val msgInfo: MsgInfo,
    val notifications: List<Camt054Notification>,
    val minorVersion: String
) {
    val msgType = "camt.054"
    val majorVersion = "001"
}

data class Camt054Notification(
    val createdOn: String,
    val bookingDate: String,
    val account: String,
    val valueDate: String,
    val transactions: List<Camt054Transaction>
)

data class Camt054Transaction(
    val entryRef: String,
    val transactionId: String,
    val amount: String,
    val creditDebitIndicator: String,
    val status: String,
    val purpose: String,
    val transactionInfo: String
)