package gr.datamation.mx.report.model

data class Pacs009Msg(
    val msgInfo: MsgInfo,
    val originalMessageId: String,
    val transactions: List<Pacs009Transaction>,
    val minorVersion: String
) {
    val msgType = "pacs.009"
    val majorVersion = "001"
}

data class Pacs009Transaction(
    val transactionId: String,
    val instructionId: String,
    val endToEndId: String,
    val interbankSettlementDebitDate: String,
    val interbankSettlementCreditDate: String,
    val interbankSettlementAmount: String,
    val instructedAmount: String,
    val purposeOfTheMessage: String,
    val internalPurposeOfTheMessage: String,
    val firstIntermediaryAgentName: String,
    val firstIntermediaryAgentId: String,
    val secondIntermediaryAgentName: String,
    val secondIntermediaryAgentId: String,
    val thirdIntermediaryAgentName: String,
    val thirdIntermediaryAgentId: String,
    val debtor: AgentInfo,
    val creditor: AgentInfo,
)