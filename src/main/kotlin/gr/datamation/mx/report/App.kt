package gr.datamation.mx.report

import gr.datamation.mx.report.model.InternalData
import java.nio.file.Path
import java.nio.file.Paths

fun main(args: Array<String>) {
    val inputArgs = InputArgs.parse(args)

    val reporter = Reporter()
    reporter.buildReport(
        inputFilePath = inputArgs.inputFile,
        outputFilePath = inputArgs.outputFile,
        internalData = InternalData(
            id = inputArgs.internalId,
            status = inputArgs.internalStatus,
        )
    )
}

data class InputArgs(
    val inputFile: Path,
    val outputFile: Path,
    val internalId: String?,
    val internalStatus: String?,
) {
    companion object {
        private fun parseArg(
            args: Array<String>,
            prefix: String,
        ): String {
            if (args.size % 2 != 0) {
                throw IllegalArgumentException("There should be a value for every argument")
            }
            val list = mutableListOf<MutableList<String>>()
            args.forEachIndexed { index, value ->
                // group the arguments by 2 to group the key-value args,
                // e.g. `-i path/to/file` would become `[["-i", "path/to/file"]]`
                when (index % 2) {
                    0 -> list.add(mutableListOf(value))
                    1 -> list[list.size - 1].add(value)
                }
            }
            val pairs = list.map { group ->
                // transform the two-value array into a list of Pair elements
                Pair(group[0], group[1])
            }
            return pairs.find { pair ->
                pair.first == prefix
            }?.second ?: throw IllegalArgumentException("Required arg missing: $prefix")
        }

        fun parse(args: Array<String>): InputArgs {
            return InputArgs(
                inputFile = Paths.get(parseArg(args, "-i")),
                outputFile = Paths.get(parseArg(args, "-o")),
                internalId = parseArg(args, "-id"),
                internalStatus = parseArg(args, "-is"),
            )
        }
    }


}