package gr.datamation.mx.report

import gr.datamation.mx.report.creator.*
import gr.datamation.mx.report.mapping.camt053.*
import gr.datamation.mx.report.mapping.camt054.*
import gr.datamation.mx.report.mapping.pacs004.*
import gr.datamation.mx.report.mapping.pacs008.*
import gr.datamation.mx.report.mapping.pacs009.*
import gr.datamation.mx.report.model.InternalData
import xsd.camt_053_001_08.BankToCustomerStatementV08
import xsd.camt_053_001_09.BankToCustomerStatementV09
import xsd.camt_054_001_08.BankToCustomerDebitCreditNotificationV08
import xsd.camt_054_001_09.BankToCustomerDebitCreditNotificationV09
import xsd.pacs_004_001_09.PaymentReturnV09
import xsd.pacs_004_001_10.PaymentReturnV10
import xsd.pacs_004_001_11.PaymentReturnV11
import xsd.pacs_008_001_08.FIToFICustomerCreditTransferV08
import xsd.pacs_008_001_09.FIToFICustomerCreditTransferV09
import xsd.pacs_008_001_10.FIToFICustomerCreditTransferV10
import xsd.pacs_009_001_08.FinancialInstitutionCreditTransferV08
import xsd.pacs_009_001_09.FinancialInstitutionCreditTransferV09
import xsd.pacs_009_001_10.FinancialInstitutionCreditTransferV10
import xsd.camt_053_001_01.BankToCustomerStatementV01
import xsd.camt_053_001_02.BankToCustomerStatementV02
import xsd.camt_053_001_03.BankToCustomerStatementV03
import xsd.camt_053_001_04.BankToCustomerStatementV04
import xsd.camt_053_001_05.BankToCustomerStatementV05
import xsd.camt_053_001_06.BankToCustomerStatementV06
import xsd.camt_053_001_07.BankToCustomerStatementV07
import xsd.camt_054_001_01.BankToCustomerDebitCreditNotificationV01
import xsd.camt_054_001_02.BankToCustomerDebitCreditNotificationV02
import xsd.camt_054_001_03.BankToCustomerDebitCreditNotificationV03
import xsd.camt_054_001_04.BankToCustomerDebitCreditNotificationV04
import xsd.camt_054_001_05.BankToCustomerDebitCreditNotificationV05
import xsd.camt_054_001_06.BankToCustomerDebitCreditNotificationV06
import xsd.camt_054_001_07.BankToCustomerDebitCreditNotificationV07
import xsd.pacs_004_001_04.PaymentReturnV04
import xsd.pacs_004_001_05.PaymentReturnV05
import xsd.pacs_004_001_06.PaymentReturnV06
import xsd.pacs_004_001_07.PaymentReturnV07
import xsd.pacs_004_001_08.PaymentReturnV08
import xsd.pacs_008_001_04.FIToFICustomerCreditTransferV04
import xsd.pacs_008_001_05.FIToFICustomerCreditTransferV05
import xsd.pacs_008_001_06.FIToFICustomerCreditTransferV06
import xsd.pacs_008_001_07.FIToFICustomerCreditTransferV07
import xsd.pacs_009_001_04.FinancialInstitutionCreditTransferV04
import xsd.pacs_009_001_05.FinancialInstitutionCreditTransferV05
import xsd.pacs_009_001_06.FinancialInstitutionCreditTransferV06
import xsd.pacs_009_001_07.FinancialInstitutionCreditTransferV07
import java.io.File
import java.util.*

interface IDispatcher {
    fun createReport(
        message: Any,
        outputFile: File,
        resourceBasePath: String,
        internalData: InternalData,
        externalData: Map<String, String>,
    )

    fun createByteArrayReport(
        message: Any,
        resourceBasePath: String,
        internalData: InternalData,
        externalData: Map<String, String>,
    ): ByteArray
}

sealed class Dispatcher {
    class DefaultDispatcher : IDispatcher {
        @Throws(IllegalArgumentException::class)
        override fun createReport(
            message: Any,
            outputFile: File,
            resourceBasePath: String,
            internalData: InternalData,
            externalData: Map<String, String>,
        ) {
            when (message) {
                is BankToCustomerStatementV01 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt053V00101().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV02 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt053V00102().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV03 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt053V00103().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV04 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt053V00104().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV05 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt053V00105().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV06 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt053V00106().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV07 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt053V00107().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV08 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt053V00108().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV09 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt053V00109().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV01 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt054V00101().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV02 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt054V00102().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV03 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt054V00103().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV04 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt054V00104().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV05 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt054V00105().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV06 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt054V00106().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV07 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt054V00107().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV08 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt054V00108().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV09 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperCamt054V00109().convertMsg(message)
                    )
                }
                is PaymentReturnV04 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs004V00104().convertMsg(message)
                    )
                }
                is PaymentReturnV05 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs004V00105().convertMsg(message)
                    )
                }
                is PaymentReturnV06 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs004V00106().convertMsg(message)
                    )
                }
                is PaymentReturnV07 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs004V00107().convertMsg(message)
                    )
                }
                is PaymentReturnV08 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs004V00108().convertMsg(message)
                    )
                }
                is PaymentReturnV09 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs004V00109().convertMsg(message)
                    )
                }
                is PaymentReturnV10 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs004V00110().convertMsg(message)
                    )
                }
                is PaymentReturnV11 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs004V00111().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV04 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs008V00104().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV05 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs008V00105().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV06 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs008V00106().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV07 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs008V00107().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV08 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs008V00108().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV09 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs008V00109().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV10 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs008V00110().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV04 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs009V00104().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV05 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs009V00105().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV06 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs009V00106().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV07 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs009V00107().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV08 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs009V00108().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV09 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs009V00109().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV10 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).create(
                        outputFile,
                        MapperPacs009V00110().convertMsg(message)
                    )
                }
                else ->
                    throw IllegalArgumentException("Message category not supported: ${message::class.java.canonicalName}")
            }
        }

        @Throws(IllegalArgumentException::class)
        override fun createByteArrayReport(
            message: Any,
            resourceBasePath: String,
            internalData: InternalData,
            externalData: Map<String, String>
        ): ByteArray {
            return when (message) {
                is BankToCustomerStatementV01 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt053V00101().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV02 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt053V00102().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV03 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt053V00103().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV04 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt053V00104().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV05 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt053V00105().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV06 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt053V00106().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV07 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt053V00107().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV08 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt053V00108().convertMsg(message)
                    )
                }
                is BankToCustomerStatementV09 -> {
                    Camt053Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt053V00109().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV01 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt054V00101().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV02 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt054V00102().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV03 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt054V00103().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV04 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt054V00104().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV05 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt054V00105().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV06 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt054V00106().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV07 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt054V00107().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV08 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt054V00108().convertMsg(message)
                    )
                }
                is BankToCustomerDebitCreditNotificationV09 -> {
                    Camt054Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperCamt054V00109().convertMsg(message)
                    )
                }
                is PaymentReturnV04 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs004V00104().convertMsg(message)
                    )
                }
                is PaymentReturnV05 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs004V00105().convertMsg(message)
                    )
                }
                is PaymentReturnV06 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs004V00106().convertMsg(message)
                    )
                }
                is PaymentReturnV07 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs004V00107().convertMsg(message)
                    )
                }
                is PaymentReturnV08 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs004V00108().convertMsg(message)
                    )
                }
                is PaymentReturnV09 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs004V00109().convertMsg(message)
                    )
                }
                is PaymentReturnV10 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs004V00110().convertMsg(message)
                    )
                }
                is PaymentReturnV11 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs004V00111().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV04 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs008V00104().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV05 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs008V00105().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV06 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs008V00106().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV07 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs008V00107().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV08 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs008V00108().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV09 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs008V00109().convertMsg(message)
                    )
                }
                is FIToFICustomerCreditTransferV10 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs008V00110().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV04 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs009V00104().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV05 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs009V00105().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV06 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs009V00106().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV07 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs009V00107().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV08 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs009V00108().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV09 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs009V00109().convertMsg(message)
                    )
                }
                is FinancialInstitutionCreditTransferV10 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData).createByteArray(
                        MapperPacs009V00110().convertMsg(message)
                    )
                }
                else ->
                    throw IllegalArgumentException("Message category not supported: ${message::class.java.canonicalName}")
            }
        }

        private fun getReporter(
            message: Any,
            resourceBasePath: String,
            internalData: InternalData,
            externalData: Map<String, String>
        ) {
            when (message) {
                is BankToCustomerStatementV01 -> {
                    Camt053Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerStatementV02 -> {
                    Camt053Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerStatementV03 -> {
                    Camt053Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerStatementV04 -> {
                    Camt053Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerStatementV05 -> {
                    Camt053Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerStatementV06 -> {
                    Camt053Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerStatementV07 -> {
                    Camt053Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerStatementV08 -> {
                    Camt053Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerStatementV09 -> {
                    Camt053Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerDebitCreditNotificationV01 -> {
                    Camt054Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerDebitCreditNotificationV02 -> {
                    Camt054Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerDebitCreditNotificationV03 -> {
                    Camt054Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerDebitCreditNotificationV04 -> {
                    Camt054Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerDebitCreditNotificationV05 -> {
                    Camt054Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerDebitCreditNotificationV06 -> {
                    Camt054Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerDebitCreditNotificationV07 -> {
                    Camt054Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerDebitCreditNotificationV08 -> {
                    Camt054Report(resourceBasePath, internalData, externalData)
                }
                is BankToCustomerDebitCreditNotificationV09 -> {
                    Camt054Report(resourceBasePath, internalData, externalData)
                }
                is PaymentReturnV04 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData)
                }
                is PaymentReturnV05 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData)
                }
                is PaymentReturnV06 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData)
                }
                is PaymentReturnV07 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData)
                }
                is PaymentReturnV08 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData)
                }
                is PaymentReturnV09 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData)
                }
                is PaymentReturnV10 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData)
                }
                is PaymentReturnV11 -> {
                    Pacs004Report(resourceBasePath, internalData, externalData)
                }
                is FIToFICustomerCreditTransferV04 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData)
                }
                is FIToFICustomerCreditTransferV05 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData)
                }
                is FIToFICustomerCreditTransferV06 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData)
                }
                is FIToFICustomerCreditTransferV07 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData)
                }
                is FIToFICustomerCreditTransferV08 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData)
                }
                is FIToFICustomerCreditTransferV09 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData)
                }
                is FIToFICustomerCreditTransferV10 -> {
                    Pacs008Report(resourceBasePath, internalData, externalData)
                }
                is FinancialInstitutionCreditTransferV04 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData)
                }
                is FinancialInstitutionCreditTransferV05 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData)
                }
                is FinancialInstitutionCreditTransferV06 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData)
                }
                is FinancialInstitutionCreditTransferV07 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData)
                }
                is FinancialInstitutionCreditTransferV08 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData)
                }
                is FinancialInstitutionCreditTransferV09 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData)
                }
                is FinancialInstitutionCreditTransferV10 -> {
                    Pacs009Report(resourceBasePath, internalData, externalData)
                }
                else ->
                    throw IllegalArgumentException("Message category not supported: ${message::class.java.canonicalName}")
            }
        }
    }
}
