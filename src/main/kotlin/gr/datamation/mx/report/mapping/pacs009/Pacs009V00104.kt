package gr.datamation.mx.report.mapping.pacs009

import gr.datamation.mx.report.creator.formatAddress
import gr.datamation.mx.report.creator.formatDate
import gr.datamation.mx.report.creator.formatMoney
import gr.datamation.mx.report.mapping.Mapper
import gr.datamation.mx.report.mapping.NO_VALUE
import gr.datamation.mx.report.model.AgentInfo
import gr.datamation.mx.report.model.MsgInfo
import gr.datamation.mx.report.model.Pacs009Msg
import gr.datamation.mx.report.model.Pacs009Transaction
import xsd.pacs_009_001_04.*

class MapperPacs009V00104 : Mapper<FinancialInstitutionCreditTransferV04, Pacs009Msg>() {
    override fun convertMsg(msg: FinancialInstitutionCreditTransferV04): Pacs009Msg {
        return Pacs009Msg(
            MsgInfo(
                creationDate = formatDate(msg.grpHdr.creDtTm),
                messageId = msg.grpHdr.msgId,
            ),
            originalMessageId = NO_VALUE,
            transactions = convertTransaction(msg.cdtTrfTxInf),
            minorVersion = "04"
        )
    }

    private fun convertTransaction(
        transactions: List<CreditTransferTransaction8>
    ): List<Pacs009Transaction> {
        return transactions.map {
            Pacs009Transaction(
                transactionId = it.pmtId.txId ?: NO_VALUE,
                instructionId = it.pmtId.instrId,
                endToEndId = it.pmtId.endToEndId,
                interbankSettlementDebitDate = formatDate(it.sttlmTmIndctn?.dbtDtTm),
                interbankSettlementCreditDate = formatDate(it.sttlmTmIndctn?.cdtDtTm),
                interbankSettlementAmount = formatCurrencyAndAmount(it.intrBkSttlmAmt),
                instructedAmount = formatCurrencyAndAmount(it.undrlygCstmrCdtTrf?.instdAmt),
                purposeOfTheMessage = NO_VALUE,
                internalPurposeOfTheMessage = formatPurpose(it.rmtInf),
                firstIntermediaryAgentName = it.intrmyAgt1?.finInstnId?.nm ?: NO_VALUE,
                firstIntermediaryAgentId = it.intrmyAgt1?.finInstnId?.bicfi ?: NO_VALUE,
                secondIntermediaryAgentName = it.intrmyAgt2?.finInstnId?.nm ?: NO_VALUE,
                secondIntermediaryAgentId = it.intrmyAgt2?.finInstnId?.bicfi ?: NO_VALUE,
                thirdIntermediaryAgentName = it.intrmyAgt3?.finInstnId?.nm ?: NO_VALUE,
                thirdIntermediaryAgentId = it.intrmyAgt3?.finInstnId?.bicfi ?: NO_VALUE,
                debtor = convertAgent(it.dbtr.finInstnId, it.dbtrAcct),
                creditor = convertAgent(it.cdtr.finInstnId, it.cdtrAcct),
            )
        }
    }

    private fun convertAgent(party: FinancialInstitutionIdentification8?, account: CashAccount24?): AgentInfo {
        return when (party) {
            null -> AgentInfo()
            else -> AgentInfo(
                name = party.nm,
                iban = account?.id?.iban ?: NO_VALUE,
                bicfi = party.bicfi,
                address = party.pstlAdr?.format(),
                country = party.pstlAdr?.ctry,
            )
        }
    }

    // tools

    private fun formatPurpose(info: RemittanceInformation2?): String {
        return info?.ustrd?.joinToString(separator = "/") ?: NO_VALUE
    }

    private fun PostalAddress6.format(): String {
        return formatAddress(bldgNb, strtNm, twnNm, pstCd) {
            if (adrLine.size > 0) adrLine.joinToString(separator = " ") else NO_VALUE
        }
    }

    private fun formatCurrencyAndAmount(amount: ActiveCurrencyAndAmount?): String {
        return formatMoney(amount?.value, amount?.ccy)
    }

    private fun formatCurrencyAndAmount(amount: ActiveOrHistoricCurrencyAndAmount?): String {
        return formatMoney(amount?.value, amount?.ccy)
    }
}