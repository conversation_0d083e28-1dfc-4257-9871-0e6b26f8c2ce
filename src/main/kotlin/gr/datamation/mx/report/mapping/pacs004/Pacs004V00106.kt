package gr.datamation.mx.report.mapping.pacs004

import gr.datamation.mx.report.creator.formatAddress
import gr.datamation.mx.report.creator.formatDate
import gr.datamation.mx.report.creator.formatMoney
import gr.datamation.mx.report.mapping.Mapper
import gr.datamation.mx.report.mapping.NO_VALUE
import gr.datamation.mx.report.model.AgentInfo
import gr.datamation.mx.report.model.MsgInfo
import gr.datamation.mx.report.model.Pacs004Msg
import gr.datamation.mx.report.model.Pacs004Transaction
import xsd.pacs_004_001_06.*

class MapperPacs004V00106 : Mapper<PaymentReturnV06, Pacs004Msg>() {
    override fun convertMsg(msg: PaymentReturnV06): Pacs004Msg {
        return Pacs004Msg(
            MsgInfo(
                creationDate = formatDate(msg.grpHdr.creDtTm),
                messageId = msg.grpHdr.msgId,
            ),
            transactions = convertTransaction(msg.orgnlGrpInf, msg.txInf),
            minorVersion = "06"
        )
    }

    private fun convertTransaction(orgnlGrpInf: OriginalGroupHeader2?, transactions: List<PaymentTransaction65>): List<Pacs004Transaction> {
        return transactions.map {
            Pacs004Transaction(
                originalMessageId = orgnlGrpInf?.orgnlMsgId ?: it.orgnlGrpInf?.orgnlMsgId ?: NO_VALUE,
                originalInterbankSettlementAmount = formatCurrencyAndAmount(it.orgnlIntrBkSttlmAmt),
                returnedInterbankSettlementAmount = formatCurrencyAndAmount(it.rtrdIntrBkSttlmAmt),
                originalTransactionId = it.orgnlTxId ?: NO_VALUE,
                originalInstructionId = it.orgnlInstrId ?: NO_VALUE,
                originalEndToEndId = it.orgnlEndToEndId ?: NO_VALUE,
                returnId = it.rtrId,
                returnReason = formatReturnReason(it.rtrRsnInf),
                debtor = convertAgent(it.orgnlTxRef?.dbtr, it.orgnlTxRef?.dbtrAgt, it.orgnlTxRef?.dbtrAcct),
                creditor = convertAgent(it.orgnlTxRef?.cdtr, it.orgnlTxRef?.cdtrAgt, it.orgnlTxRef?.cdtrAcct),
            )
        }
    }

    private fun convertAgent(
        party: PartyIdentification43?,
        agent: BranchAndFinancialInstitutionIdentification5?,
        account: CashAccount24?
    ): AgentInfo {
        return when (party) {
            null -> AgentInfo()
            else -> AgentInfo(
                name = party.nm,
                iban = account?.id?.iban ?: NO_VALUE,
                bicfi = agent?.finInstnId?.bicfi ?: NO_VALUE,
                address = party.pstlAdr.format {
                    agent?.finInstnId?.pstlAdr?.format()
                },
                country = party.pstlAdr.ctry,
            )
        }
    }

    // tools

    private fun PostalAddress6.format(orElse: () -> String? = { NO_VALUE }): String {
        return formatAddress(bldgNb, strtNm, twnNm, pstCd) {
            if (adrLine.size > 0) adrLine.joinToString(separator = " ") else orElse()
        }
    }

    private fun formatReturnReason(returnReasons: List<PaymentReturnReason1>): String {
        return returnReasons.joinToString(separator = ",") { it.rsn.cd }
    }

    private fun formatCurrencyAndAmount(amount: ActiveCurrencyAndAmount?): String {
        return formatMoney(amount?.value, amount?.ccy)
    }

    private fun formatCurrencyAndAmount(amount: ActiveOrHistoricCurrencyAndAmount?): String {
        return formatMoney(amount?.value, amount?.ccy)
    }
}