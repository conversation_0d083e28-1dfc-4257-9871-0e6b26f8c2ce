package gr.datamation.mx.report.mapping.camt054

import gr.datamation.mx.report.creator.formatDate
import gr.datamation.mx.report.creator.formatMoney
import gr.datamation.mx.report.mapping.Mapper
import gr.datamation.mx.report.mapping.NO_ACCOUNT_ID
import gr.datamation.mx.report.mapping.NO_TRX_ID
import gr.datamation.mx.report.mapping.NO_VALUE
import gr.datamation.mx.report.model.Camt054Msg
import gr.datamation.mx.report.model.Camt054Notification
import gr.datamation.mx.report.model.Camt054Transaction
import gr.datamation.mx.report.model.MsgInfo
import xsd.camt_054_001_05.*

class MapperCamt054V00105 : Mapper<BankToCustomerDebitCreditNotificationV05, Camt054Msg>() {
    override fun convertMsg(msg: BankToCustomerDebitCreditNotificationV05): Camt054Msg {
        return Camt054Msg(
            MsgInfo(
                creationDate = formatDate(msg.grpHdr.creDtTm),
                messageId = msg.grpHdr.msgId,
            ),
            notifications = msg.ntfctn.map {
                convertNotification(it)
            },
            minorVersion = "05"
        )
    }

    private fun convertNotification(notification: AccountNotification11): Camt054Notification {
        val bookingNotificationEntry = notification.ntry.find { it.sts == EntryStatus2Code.BOOK }

        return Camt054Notification(
            createdOn = formatDate(notification.creDtTm),
            bookingDate = when (bookingNotificationEntry) {
                null -> NO_VALUE
                else -> formatDate(bookingNotificationEntry.bookgDt.dt ?: bookingNotificationEntry.bookgDt.dtTm)
            },
            account = formatAccountId(notification.acct),
            valueDate = when (bookingNotificationEntry) {
                null -> NO_VALUE
                else -> formatDate(bookingNotificationEntry.valDt.dt ?: bookingNotificationEntry.valDt.dtTm)
            },
            transactions = notification.ntry.map { entry ->
                convertTransactions(entry)
            }.flatten()
        )
    }


    private fun convertTransactions(entry: ReportEntry7): List<Camt054Transaction> {
        return entry.ntryDtls.map { entryDetails ->
            entryDetails.txDtls.map {
                Camt054Transaction(
                    entryRef = entry.acctSvcrRef ?: NO_VALUE,
                    transactionId = getEntryTransactionId(entry),
                    amount = formatCurrencyAndAmount(entry.amt),
                    creditDebitIndicator = formatCreditDebitCode(entry.cdtDbtInd),
                    status = entry.sts?.value() ?: NO_VALUE,
                    purpose = it.purp?.cd ?: it.purp?.prtry ?: NO_VALUE,
                    transactionInfo = entry.addtlNtryInf ?: ""
                )
            }
        }.flatten()
    }

// tools

    private fun formatCurrencyAndAmount(amount: ActiveOrHistoricCurrencyAndAmount?): String {
        return formatMoney(amount?.value, amount?.ccy)
    }

    private fun formatAccountId(account: CashAccount25): String {
        return when {
            account.id.iban != null -> "${account.id.iban} (IBAN)"
            else -> NO_ACCOUNT_ID
        }
    }

    private fun formatCreditDebitCode(code: CreditDebitCode): String {
        return when (code) {
            CreditDebitCode.CRDT -> "+"
            CreditDebitCode.DBIT -> "-"
        }
    }

    private fun getEntryTransactionId(entry: ReportEntry7): String {
        return when {
            // TODO check what's the logic if transactionDetails or batches have multiple members
            entry.ntryDtls.size > 0 -> when {
                entry.ntryDtls[0].txDtls.size > 0 -> formatTransactionId(entry.ntryDtls[0].txDtls[0].refs)
                entry.ntryDtls[0].btch != null -> entry.ntryDtls[0].btch.msgId
                else -> NO_TRX_ID
            }
            else -> NO_TRX_ID
        }
    }

    private fun formatTransactionId(refs: TransactionReferences3): String {
        return when {
            refs.txId != null -> refs.txId
            refs.acctSvcrRef != null -> refs.acctSvcrRef
            refs.msgId != null -> refs.msgId
            refs.endToEndId != null -> refs.endToEndId
            else -> NO_TRX_ID
        }
    }
}