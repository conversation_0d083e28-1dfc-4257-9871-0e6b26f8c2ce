package gr.datamation.mx.report.mapping.camt053

import gr.datamation.mx.report.creator.formatDate
import gr.datamation.mx.report.creator.formatMoney
import gr.datamation.mx.report.mapping.Mapper
import gr.datamation.mx.report.mapping.NO_ACCOUNT_ID
import gr.datamation.mx.report.mapping.NO_TRX_ID
import gr.datamation.mx.report.mapping.NO_VALUE
import gr.datamation.mx.report.model.Camt053Msg
import gr.datamation.mx.report.model.Camt053Statement
import gr.datamation.mx.report.model.Camt053Transaction
import gr.datamation.mx.report.model.MsgInfo
import xsd.camt_053_001_01.*

class MapperCamt053V00101 : Mapper<BankToCustomerStatementV01, Camt053Msg>() {
    override fun convertMsg(msg: BankToCustomerStatementV01): Camt053Msg {
        return Camt053Msg(
            MsgInfo(
                creationDate = formatDate(msg.grpHdr.creDtTm),
                messageId = msg.grpHdr.msgId,
            ),
            statements = msg.stmt.map {
                convertStatement(it)
            },
            minorVersion = "01"
        )
    }

    fun convertStatement(statement: AccountStatement1): Camt053Statement {
        val openingBalanceValue = findBalanceByType(statement, BalanceType9Code.OPBD)
        val closingBalanceValue = findBalanceByType(statement, BalanceType9Code.CLBD)
        val availableOpeningBalanceValue = findBalanceByType(statement, BalanceType9Code.OPAV)
        val availableClosingBalanceValue = findBalanceByType(statement, BalanceType9Code.CLAV)

        return Camt053Statement(
            createdOn = formatDate(statement.creDtTm),
            openingBalance = formatCashBalance(openingBalanceValue),
            closingBalance = formatCashBalance(closingBalanceValue),
            availableOpeningBalance = formatCashBalance(availableOpeningBalanceValue),
            availableClosingBalance = formatCashBalance(availableClosingBalanceValue),
            account = formatAccountId(statement.acct),
            from = formatDate(statement.frToDt.frDtTm),
            to = formatDate(statement.frToDt.toDtTm),
            transactions = statement.ntry.map {
                convertStatement(it)
            }
        )
    }


    fun convertStatement(entry: StatementEntry1): Camt053Transaction {
        return Camt053Transaction(
            entryRef = entry.acctSvcrRef ?: NO_VALUE,
            transactionId = getEntryTransactionId(entry),
            transactionCreationDate = formatDate(entry.valDt.dt),
            amount = formatCurrencyAndAmount(entry.amt),
            creditDebitIndicator = formatCreditDebitCode(entry.cdtDbtInd),
            transactionInfo = entry.addtlNtryInf ?: ""
        )
    }

// tools

    fun findBalanceByType(statement: AccountStatement1, type: BalanceType9Code): CashBalance2? {
        return statement.bal.find { it.tp?.cd == type }
    }

    fun formatCashBalance(amount: CashBalance2?): String {
        return formatCurrencyAndAmount(amount?.amt)
    }

    fun formatCurrencyAndAmount(amount: CurrencyAndAmount?): String {
        return formatMoney(amount?.value, amount?.ccy)
    }

    fun formatAccountId(account: CashAccount13): String {
        return when {
            account.id.iban != null -> "${account.id.iban} (IBAN)"
            account.id.bban != null -> "${account.id.bban} (BBAN)"
            account.id.upic != null -> "${account.id.upic} (UPIC)"
            account.id.prtryAcct != null -> account.id.prtryAcct.id
            else -> NO_ACCOUNT_ID
        }
    }

    fun formatCreditDebitCode(code: CreditDebitCode): String {
        return when (code) {
            CreditDebitCode.CRDT -> "+"
            CreditDebitCode.DBIT -> "-"
        }
    }

    fun formatTransactionId(refs: TransactionReferences1): String {
        return when {
            refs.txId != null -> refs.txId
            refs.acctSvcrRef != null -> refs.acctSvcrRef
            refs.msgId != null -> refs.msgId
            refs.endToEndId != null -> refs.endToEndId
            else -> NO_TRX_ID
        }
    }

    fun getEntryTransactionId(entry: StatementEntry1): String {
        return when {
            // TODO check what's the logic if transactionDetails or batches have multiple members
            entry.txDtls.size > 0 -> formatTransactionId(entry.txDtls[0].refs)
            entry.btch.size > 0 -> entry.btch[0].msgId
            else -> NO_TRX_ID
        }
    }
}