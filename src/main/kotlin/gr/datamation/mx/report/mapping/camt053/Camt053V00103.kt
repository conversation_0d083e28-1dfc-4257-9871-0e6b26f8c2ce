package gr.datamation.mx.report.mapping.camt053

import gr.datamation.mx.report.creator.formatDate
import gr.datamation.mx.report.creator.formatMoney
import gr.datamation.mx.report.mapping.Mapper
import gr.datamation.mx.report.mapping.NO_ACCOUNT_ID
import gr.datamation.mx.report.mapping.NO_TRX_ID
import gr.datamation.mx.report.mapping.NO_VALUE
import gr.datamation.mx.report.model.Camt053Msg
import gr.datamation.mx.report.model.Camt053Statement
import gr.datamation.mx.report.model.Camt053Transaction
import gr.datamation.mx.report.model.MsgInfo
import xsd.camt_053_001_03.*

class MapperCamt053V00103 : Mapper<BankToCustomerStatementV03, Camt053Msg>() {
    override fun convertMsg(msg: BankToCustomerStatementV03): Camt053Msg {
        return Camt053Msg(
            MsgInfo(
                creationDate = formatDate(msg.grpHdr.creDtTm),
                messageId = msg.grpHdr.msgId,
            ),
            statements = msg.stmt.map {
                convertStatement(it)
            },
            minorVersion = "03"
        )
    }

    fun convertStatement(statement: AccountStatement3): Camt053Statement {
        val openingBalanceValue = findBalanceByType(statement, BalanceType12Code.OPBD)
        val closingBalanceValue = findBalanceByType(statement, BalanceType12Code.CLBD)
        val availableOpeningBalanceValue = findBalanceByType(statement, BalanceType12Code.OPAV)
        val availableClosingBalanceValue = findBalanceByType(statement, BalanceType12Code.CLAV)

        return Camt053Statement(
            createdOn = formatDate(statement.creDtTm),
            openingBalance = formatCashBalance(openingBalanceValue),
            closingBalance = formatCashBalance(closingBalanceValue),
            availableOpeningBalance = formatCashBalance(availableOpeningBalanceValue),
            availableClosingBalance = formatCashBalance(availableClosingBalanceValue),
            account = formatAccountId(statement.acct),
            from = formatDate(statement.frToDt.frDtTm),
            to = formatDate(statement.frToDt.toDtTm),
            transactions = statement.ntry.map {
                convertTransaction(it)
            }
        )
    }

    fun convertTransaction(entry: ReportEntry3): Camt053Transaction {
        return Camt053Transaction(
            entryRef = entry.acctSvcrRef ?: NO_VALUE,
            transactionId = getEntryTransactionId(entry),
            transactionCreationDate = formatDate(entry.valDt.dt),
            amount = formatCurrencyAndAmount(entry.amt),
            creditDebitIndicator = formatCreditDebitCode(entry.cdtDbtInd),
            transactionInfo = entry.addtlNtryInf ?: ""
        )
    }


// tools

    fun findBalanceByType(statement: AccountStatement3, type: BalanceType12Code): CashBalance3? {
        return statement.bal.find { it.tp?.cdOrPrtry?.cd == type }
    }

    fun formatCashBalance(amount: CashBalance3?): String {
        return formatCurrencyAndAmount(amount?.amt)
    }

    fun formatCurrencyAndAmount(amount: ActiveOrHistoricCurrencyAndAmount?): String {
        return formatMoney(amount?.value, amount?.ccy)
    }

    fun formatAccountId(account: CashAccount25): String {
        return when {
            account.id.iban != null -> account.id.iban
            account.id.othr != null -> account.id.othr.id
            else -> NO_ACCOUNT_ID
        }
    }

    fun formatCreditDebitCode(code: CreditDebitCode): String {
        return when (code) {
            CreditDebitCode.CRDT -> "+"
            CreditDebitCode.DBIT -> "-"
        }
    }

    fun formatTransactionId(refs: TransactionReferences3): String {
        return when {
            refs.txId != null -> refs.txId
            refs.acctSvcrRef != null -> refs.acctSvcrRef
            refs.msgId != null -> refs.msgId
            refs.endToEndId != null -> refs.endToEndId
            else -> NO_TRX_ID
        }
    }

    fun getEntryTransactionId(entry: ReportEntry3): String {
        return when {
            // TODO check what's the logic if transactionDetails or batches have multiple members
            entry.ntryDtls.size > 0 -> when {
                entry.ntryDtls[0].txDtls.size > 0 -> formatTransactionId(entry.ntryDtls[0].txDtls[0].refs)
                entry.ntryDtls[0].btch != null -> entry.ntryDtls[0].btch.msgId
                else -> NO_TRX_ID
            }
            else -> NO_TRX_ID
        }
    }
}