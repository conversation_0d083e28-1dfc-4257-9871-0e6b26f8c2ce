package gr.datamation.mx.report.mapping.pacs004

import gr.datamation.mx.report.creator.formatAddress
import gr.datamation.mx.report.creator.formatDate
import gr.datamation.mx.report.creator.formatMoney
import gr.datamation.mx.report.mapping.Mapper
import gr.datamation.mx.report.mapping.NO_VALUE
import gr.datamation.mx.report.model.AgentInfo
import gr.datamation.mx.report.model.MsgInfo
import gr.datamation.mx.report.model.Pacs004Msg
import gr.datamation.mx.report.model.Pacs004Transaction
import xsd.pacs_004_001_11.PostalAddress24
import xsd.pacs_004_001_08.*

class MapperPacs004V00108 : Mapper<PaymentReturnV08, Pacs004Msg>() {
    override fun convertMsg(msg: PaymentReturnV08): Pacs004Msg {
        return Pacs004Msg(
            MsgInfo(
                creationDate = formatDate(msg.grpHdr.creDtTm),
                messageId = msg.grpHdr.msgId,
            ),
            transactions = convertTransaction(msg.orgnlGrpInf, msg.txInf),
            minorVersion = "08"
        )
    }

    private fun convertTransaction(orgnlGrpInf: OriginalGroupHeader12?, transactions: List<PaymentTransaction87>): List<Pacs004Transaction> {
        return transactions.map {
            Pacs004Transaction(
                originalMessageId = orgnlGrpInf?.orgnlMsgId ?: it.orgnlGrpInf?.orgnlMsgId ?: NO_VALUE,
                originalInterbankSettlementAmount = formatCurrencyAndAmount(it.orgnlIntrBkSttlmAmt),
                returnedInterbankSettlementAmount = formatCurrencyAndAmount(it.rtrdIntrBkSttlmAmt),
                originalTransactionId = it.orgnlTxId ?: NO_VALUE,
                originalInstructionId = it.orgnlInstrId ?: NO_VALUE,
                originalEndToEndId = it.orgnlEndToEndId ?: NO_VALUE,
                returnId = it.rtrId,
                returnReason = formatReturnReason(it.rtrRsnInf),
                debtor = convertAgent(it.orgnlTxRef?.dbtr, it.orgnlTxRef?.dbtrAgt, it.orgnlTxRef?.dbtrAcct),
                creditor = convertAgent(it.orgnlTxRef?.cdtr, it.orgnlTxRef?.cdtrAgt, it.orgnlTxRef?.cdtrAcct),
            )
        }
    }

    private fun convertAgent(
        party: Party35Choice?,
        agent: BranchAndFinancialInstitutionIdentification5?,
        account: CashAccount24?,
    ): AgentInfo {
        return when (party) {
            null -> AgentInfo()
            else -> when {
                party.pty != null -> AgentInfo(
                    name = party.pty.nm,
                    iban = account?.id?.iban,
                    bicfi = agent?.finInstnId?.bicfi,
                    address = party.pty.pstlAdr.format(),
                    country = party.pty.pstlAdr.ctry,
                )
                party.agt != null -> AgentInfo(
                    name = party.agt.brnchId?.nm ?: NO_VALUE,
                    iban = party.agt.brnchId?.id ?: NO_VALUE,
                    bicfi = party.agt.finInstnId?.bicfi ?: NO_VALUE,
                    address = party.agt.brnchId?.pstlAdr?.format {
                        agent?.finInstnId?.pstlAdr?.format()
                    },
                    country = party.agt.brnchId?.pstlAdr?.ctry ?: NO_VALUE,
                )
                else -> AgentInfo()
            }
        }
    }

    // tools

    private fun PostalAddress6.format(orElse: () -> String? = { NO_VALUE }): String {
        return formatAddress(bldgNb, strtNm, twnNm, pstCd) {
            if (adrLine.size > 0) adrLine.joinToString(separator = " ") else orElse()
        }
    }

    private fun formatReturnReason(returnReasons: List<PaymentReturnReason4>): String {
        return returnReasons.joinToString(separator = ",") { it.rsn.cd }
    }

    private fun formatCurrencyAndAmount(amount: ActiveCurrencyAndAmount?): String {
        return formatMoney(amount?.value, amount?.ccy)
    }

    private fun formatCurrencyAndAmount(amount: ActiveOrHistoricCurrencyAndAmount?): String {
        return formatMoney(amount?.value, amount?.ccy)
    }
}