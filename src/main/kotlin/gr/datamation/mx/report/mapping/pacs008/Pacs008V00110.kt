package gr.datamation.mx.report.mapping.pacs008

import gr.datamation.mx.report.creator.formatAddress
import gr.datamation.mx.report.creator.formatDate
import gr.datamation.mx.report.creator.formatMoney
import gr.datamation.mx.report.mapping.Mapper
import gr.datamation.mx.report.mapping.NO_TRX_ID
import gr.datamation.mx.report.mapping.NO_VALUE
import gr.datamation.mx.report.model.AgentInfo
import gr.datamation.mx.report.model.MsgInfo
import gr.datamation.mx.report.model.Pacs008Msg
import gr.datamation.mx.report.model.Pacs008Transaction
import xsd.pacs_008_001_10.*

class MapperPacs008V00110 : Mapper<FIToFICustomerCreditTransferV10, Pacs008Msg>() {
    override fun convertMsg(msg: FIToFICustomerCreditTransferV10): Pacs008Msg {
        return Pacs008Msg(
            MsgInfo(
                creationDate = formatDate(msg.grpHdr.creDtTm),
                messageId = msg.grpHdr.msgId,
            ),
            transactions = convertTransaction(msg.grpHdr, msg.cdtTrfTxInf),
            minorVersion = "10"
        )
    }

    private fun convertTransaction(
        groupHeader: GroupHeader96, transactions: List<CreditTransferTransaction50>
    ): List<Pacs008Transaction> {
        return transactions.map {
            Pacs008Transaction(
                transactionId = it.pmtId.txId ?: NO_TRX_ID,
                instructionId = it.pmtId.instrId ?: NO_TRX_ID,
                endToEndId = it.pmtId.endToEndId,
                interbankSettlementDate = when (it.intrBkSttlmDt) {
                    null -> formatDate(groupHeader.intrBkSttlmDt)
                    else -> formatDate(it.intrBkSttlmDt)
                },
                interbankSettlementAmount = formatCurrencyAndAmount(it.intrBkSttlmAmt),
                instructedAmount = formatCurrencyAndAmount(it.instdAmt),
                exchangeRate = it.xchgRate?.toString() ?: NO_VALUE,
                chargeBearer = it.chrgBr?.value() ?: NO_VALUE,
                chargesInformation = formatCharges(it.chrgsInf),
                purposeOfTheMessage = it.purp?.cd ?: it.purp?.prtry ?: NO_VALUE,
                internalPurposeOfTheMessage = formatInternalPurpose(it.rmtInf?.strd),
                firstIntermediaryAgentName = it.intrmyAgt1?.finInstnId?.nm ?: NO_VALUE,
                firstIntermediaryAgentId = it.intrmyAgt1?.finInstnId?.bicfi ?: NO_VALUE,
                secondIntermediaryAgentName = it.intrmyAgt2?.finInstnId?.nm ?: NO_VALUE,
                secondIntermediaryAgentId = it.intrmyAgt2?.finInstnId?.bicfi ?: NO_VALUE,
                thirdIntermediaryAgentName = it.intrmyAgt3?.finInstnId?.nm ?: NO_VALUE,
                thirdIntermediaryAgentId = it.intrmyAgt3?.finInstnId?.bicfi ?: NO_VALUE,
                debtor = convertAgent(it.dbtr, it.dbtrAgt, it.dbtrAcct),
                creditor = convertAgent(it.cdtr, it.cdtrAgt, it.cdtrAcct),
                remittanceInformation = it.rmtInf?.ustrd?.joinToString(separator = "") ?: NO_VALUE,
            )
        }
    }

    private fun convertAgent(
        party: PartyIdentification135?,
        agent: BranchAndFinancialInstitutionIdentification6?,
        account: CashAccount40?
    ): AgentInfo {
        return when (party) {
            null -> AgentInfo()
            else -> AgentInfo(
                name = party.nm,
                iban = account?.id?.iban,
                bicfi = agent?.finInstnId?.bicfi,
                address = party.pstlAdr?.format(),
                country = party.pstlAdr?.ctry,
            )
        }
    }

    // tools

    private fun formatCharges(charges: List<Charges7>): String {
        return charges.map {
            it.amt
        }.joinToString(separator = "/")
    }

    private fun formatInternalPurpose(inf: List<StructuredRemittanceInformation17> ?): String {
        return inf?.joinToString(separator = ",") { it.addtlRmtInf.joinToString(separator = "/") } ?: ""
    }

    private fun PostalAddress24.format(orElse: () -> String? = { NO_VALUE }): String {
        return formatAddress(bldgNb, strtNm, twnNm, pstCd) {
            if (adrLine.size > 0) adrLine.joinToString(separator = " ") else orElse()
        }
    }

    private fun formatCurrencyAndAmount(amount: ActiveCurrencyAndAmount?): String {
        return formatMoney(amount?.value, amount?.ccy)
    }

    private fun formatCurrencyAndAmount(amount: ActiveOrHistoricCurrencyAndAmount?): String {
        return formatMoney(amount?.value, amount?.ccy)
    }
}