package gr.datamation.mx.report.layout

import org.apache.pdfbox.pdmodel.PDDocument
import org.apache.pdfbox.pdmodel.PDPage
import org.apache.pdfbox.pdmodel.common.PDRectangle
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder
import gr.datamation.mx.report.creator.formatDate
import gr.datamation.mx.report.creator.formatTime
import gr.datamation.mx.report.creator.styledHtml
import org.celtric.kotlin.html.div
import java.io.ByteArrayOutputStream
import java.util.*

class DefaultLayout(val resourceBasePath: String) {
    companion object {
        const val margin = 45f
    }

    fun <T> withDocument(pdfDoc: PDDocument, fn: () -> T): T {
        // Add initial page if document is empty
        if (pdfDoc.numberOfPages == 0) {
            pdfDoc.addPage(PDPage(PDRectangle.A4))
        }

        return fn()
    }

    fun addHtmlToDocument(document: PDDocument, htmlContent: String) {
        // For now, we'll store the HTML content and process it later
        // This is a simplified approach - in a full implementation,
        // you would need to properly convert HTML to PDFBox elements

        // Create a new page for the content
        val page = PDPage(PDRectangle.A4)
        document.addPage(page)

        // Note: This is a placeholder implementation
        // A complete implementation would require:
        // 1. HTML parsing and rendering to PDFBox elements
        // 2. Proper text layout and formatting
        // 3. CSS style processing
        // 4. Image handling

        println("Adding HTML content to document: ${htmlContent.take(100)}...")
    }

    private fun createCompleteHtml(content: String, currentPageNumber: Int): String {
        val header = createHeaderHtml()
        val footer = createFooterHtml(currentPageNumber)

        return """
            <!DOCTYPE html>
            <html>
            <head>
                <link rel="stylesheet" type="text/css" href="css/style.css">
                <style>
                    @page {
                        margin-top: ${margin + 80}px;
                        margin-bottom: ${margin + 30}px;
                        margin-left: ${margin}px;
                        margin-right: ${margin}px;
                    }
                    .header { position: fixed; top: 0; width: 100%; }
                    .footer { position: fixed; bottom: 0; width: 100%; }
                </style>
            </head>
            <body>
                <div class="header">$header</div>
                <div class="content">$content</div>
                <div class="footer">$footer</div>
            </body>
            </html>
        """.trimIndent()
    }

    private fun createHeaderHtml(): String {
        val date = Date()
        val dateString: String = formatDate(date)
        val timeString: String = formatTime(date)

        return styledHtml {
            div(classes = "header-container") {
                div { dateString } + div { timeString }
            }
        }.render()
    }

    private fun createFooterHtml(currentPageNumber: Int): String {
        return styledHtml {
            div(classes = "footer-container") {
                div(classes = "left") {
                    "SwiftMX Report"
                } + div(classes = "right") {
                    "$currentPageNumber"
                }
            }
        }.render()
    }
}