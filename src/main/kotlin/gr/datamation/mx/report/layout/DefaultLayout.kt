package gr.datamation.mx.report.layout

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder
import gr.datamation.mx.report.creator.formatDate
import gr.datamation.mx.report.creator.formatTime
import gr.datamation.mx.report.creator.styledHtml
import org.apache.pdfbox.multipdf.PDFMergerUtility
import org.apache.pdfbox.pdmodel.PDDocument
import org.apache.pdfbox.pdmodel.PDPage
import org.apache.pdfbox.pdmodel.common.PDRectangle
import org.celtric.kotlin.html.div
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.File
import java.util.*

class DefaultLayout(val resourceBasePath: String) {
    companion object {
        const val margin = 45f
    }

    fun <T> withDocument(pdfDoc: PDDocument, fn: () -> T): T {
        if (pdfDoc.numberOfPages == 0) {
            // add a blank page if document is empty
            pdfDoc.addPage(PDPage(PDRectangle.A4))
        }

        return fn()
    }

    fun addHtmlToDocument(document: PDDocument, htmlContent: String) {
        try {
            val completeHtml = createCompleteHtml(htmlContent, document.numberOfPages + 1)

            val htmlPdfStream = ByteArrayOutputStream()
            val builder = PdfRendererBuilder()

            builder.withHtmlContent(completeHtml, "file://${File(resourceBasePath).absolutePath}/")
            builder.toStream(htmlPdfStream)
            builder.useFastMode()

            builder.run()

            val htmlPdfBytes = htmlPdfStream.toByteArray()
            if (htmlPdfBytes.isNotEmpty()) {
                val htmlPdfDocument = PDDocument.load(ByteArrayInputStream(htmlPdfBytes))

                val merger = PDFMergerUtility()
                merger.appendDocument(document, htmlPdfDocument)

                htmlPdfDocument.close()
            }

        } catch (e: Exception) {
            println("Failed to convert HTML to PDF: ${e.message}")
            e.printStackTrace()
            document.addPage(PDPage(PDRectangle.A4))
        }
    }

    private fun createCompleteHtml(content: String, currentPageNumber: Int): String {
        val header = createHeaderHtml()
        val footer = createFooterHtml(currentPageNumber)

        return """
            <!DOCTYPE html>
            <html>
            <head>
                <link rel="stylesheet" type="text/css" href="css/style.css">
                <style>
                    @page {
                        margin-top: ${margin + 80}px;
                        margin-bottom: ${margin + 30}px;
                        margin-left: ${margin}px;
                        margin-right: ${margin}px;
                    }
                    .header { position: fixed; top: 0; width: 100%; }
                    .footer { position: fixed; bottom: 0; width: 100%; }
                </style>
            </head>
            <body>
                <div class="header">$header</div>
                <div class="content">$content</div>
                <div class="footer">$footer</div>
            </body>
            </html>
        """.trimIndent()
    }

    private fun createHeaderHtml(): String {
        val date = Date()
        val dateString: String = formatDate(date)
        val timeString: String = formatTime(date)

        return styledHtml {
            div(classes = "header-container") {
                div { dateString } + div { timeString }
            }
        }.render()
    }

    private fun createFooterHtml(currentPageNumber: Int): String {
        return styledHtml {
            div(classes = "footer-container") {
                div(classes = "left") {
                    "SwiftMX Report"
                } + div(classes = "right") {
                    "$currentPageNumber"
                }
            }
        }.render()
    }
}