package gr.datamation.mx.report.layout

import com.itextpdf.kernel.events.Event
import com.itextpdf.kernel.events.IEventHandler
import com.itextpdf.kernel.events.PdfDocumentEvent
import com.itextpdf.kernel.geom.Rectangle
import com.itextpdf.kernel.pdf.PdfDocument
import com.itextpdf.kernel.pdf.canvas.PdfCanvas
import com.itextpdf.kernel.pdf.xobject.PdfFormXObject
import com.itextpdf.layout.Canvas
import com.itextpdf.layout.Document
import gr.datamation.mx.report.creator.addHtml
import gr.datamation.mx.report.creator.formatDate
import gr.datamation.mx.report.creator.formatTime
import gr.datamation.mx.report.creator.styledHtml
import org.celtric.kotlin.html.div
import java.util.*

class DefaultLayout(val resourceBasePath: String) {
    companion object {
        const val margin = 45f
    }

    fun <T> withDocument(pdfDoc: PdfDocument, fn: (Document) -> T): T {
        val document = Document(pdfDoc)

        val headerHandler = Header(resourceBasePath)
        val footerHandler = Footer(resourceBasePath)

        document.setMargins(
            margin + headerHandler.getHeight(),
            margin,
            margin + footerHandler.getHeight(),
            margin
        )

        pdfDoc.addEventHandler(PdfDocumentEvent.START_PAGE, headerHandler)
        pdfDoc.addEventHandler(PdfDocumentEvent.END_PAGE, footerHandler)

        val res = fn(document)

        // fill the placeholder with the number of pages
        footerHandler.fillPlaceholder(pdfDoc)
        document.close()

        return res
    }

    class Header(private val resourceBasePath: String) : PageArea() {
        companion object {
            const val MAX_HEIGHT = 80f
        }

        override fun getHeight(): Float {
            return MAX_HEIGHT
        }

        override fun getArea(pageSize: Rectangle): Rectangle {
            // the position (0, 0) on the PDF page starts in the lower left corner,
            // as opposed to CSS, where (0, 0) is located in the top left corner
            return Rectangle(
                margin,
                pageSize.height - margin - getHeight(),
                pageSize.width - (margin * 2),
                getHeight()
            )
        }

        override fun addContent(pdfCanvas: PdfCanvas, canvas: Canvas, pageSize: Rectangle, currentPageNumber: Int) {
            val contentHtml = styledHtml {
                val date = Date()
                val dateString: String = formatDate(date)
                val timeString: String = formatTime(date)

                div(classes = "header-container") {
                    div { dateString } + div { timeString }
                }
            }.render()

            canvas.addHtml(contentHtml, resourceBasePath)

            canvas.close()
        }
    }

    class Footer(private val resourceBasePath: String) : PageArea() {
        private var placeholder = PdfFormXObject(Rectangle(0f, 0f, PLACEHOLDER_WIDTH, PLACEHOLDER_HEIGHT))

        companion object {
            const val MAX_HEIGHT = 30f
            const val PLACEHOLDER_WIDTH = 30f
            const val PLACEHOLDER_HEIGHT = 30f
        }

        override fun getHeight(): Float {
            return MAX_HEIGHT
        }

        override fun getArea(pageSize: Rectangle): Rectangle {
            // the position (0, 0) on the PDF page starts in the lower left corner,
            // as opposed to CSS, where (0, 0) is located in the top left corner
            return Rectangle(
                margin,
                margin - getHeight(),
                pageSize.width - (margin * 2),
                getHeight()
            )
        }

        override fun addContent(pdfCanvas: PdfCanvas, canvas: Canvas, pageSize: Rectangle, currentPageNumber: Int) {
            canvas.addHtml(resourceBasePath) {
                div(classes = "footer-container") {
                    div(classes = "left") {
                        "SwiftMX Report"
                    } + div(classes = "right") {
                        "$currentPageNumber / "
                    }
                }
            }

            // add a placeholder to hold the total number of pages;
            // it cannot be filled in the first pass, only at the end
            pdfCanvas.addXObjectAt(placeholder, pageSize.width - margin, margin - getHeight())
            pdfCanvas.release()
        }

        fun fillPlaceholder(pdf: PdfDocument) {
            val canvas = Canvas(placeholder, pdf)

            canvas.addHtml(resourceBasePath) {
                div(classes = "total-pages") { "&nbsp;${pdf.numberOfPages}" }
            }

            canvas.close()
        }
    }

    abstract class PageArea : IEventHandler {

        abstract fun getHeight(): Float

        abstract fun getArea(pageSize: Rectangle): Rectangle

        abstract fun addContent(pdfCanvas: PdfCanvas, canvas: Canvas, pageSize: Rectangle, currentPageNumber: Int)

        override fun handleEvent(event: Event) {
            val docEvent = event as PdfDocumentEvent
            val page = docEvent.page
            val pageSize = page.pageSize
            val currentPageNumber = docEvent.document.getPageNumber(page)

            val area = getArea(pageSize)

            val pdfCanvas = PdfCanvas(page)
            val canvas = Canvas(pdfCanvas, area)

            addContent(pdfCanvas, canvas, pageSize, currentPageNumber)
        }
    }
}