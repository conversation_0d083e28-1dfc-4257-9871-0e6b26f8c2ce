html {
    font: normal normal 12px/16px Arial, Helvetica, sans-serif;
    font-weight: 100;
    color: #000000;
}

h1 {
    font-size: 23px;
    font-weight: bold;
    margin: 30px 0 10px 0;
    color: #001B58;
}

h2 {
    font-size: 17px;
    font-weight: 100;
    margin: 30px 0 5px 0;
    color: #001B58;
}

table {
    background-color: #EDEDED;
    width: 100%;
    border-spacing: 0;
}

table.with-border {
    border-top: #707070 solid 1px;
}

table.with-offset {
    padding-top: 18px;
}

tr.odd {
    background-color: #EDEDED;
}

tr.even {
    background-color: #FFFFFF;
}

th {
    text-align: left;
    padding: 9px;
    background-color: #C8D9EF;
}

td {
    padding: 9px;
}

.label {
    font-weight: bold;
}

.header-container {
    /* background-color: yellow; */
    width: 100%;
    height: 80px;
    text-align: right;

    background-image: url("../img/<EMAIL>");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: top left;
}

.footer-container {
    /* background-color: yellow; */
    display: flex;
    width: 100%;
    justify-content: space-around;
}

.left {
    /* background-color: red; */
    width: 50%;
    text-align: left;
}

.right {
    /* background-color: blue; */
    width: 50%;
    text-align: right;
}

.total-pages {
    /* background-color: green; */
}

.row-div {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 20px;
}

.row-div > div {
    width: 50%;
}
.row-div > div.left-col {
    padding-right: 10px;
}
.row-div > div.right-col {
    padding-left: 10px;
}