plugins {
    id "org.jetbrains.kotlin.jvm" version "1.7.10"
    id "java"
    id "com.github.johnrengelman.shadow" version "7.1.2"
    id "org.sonarqube" version "3.3"
    id "org.owasp.dependencycheck" version "7.0.4.1"
    id "jacoco"
    id "maven-publish"
}

def kotlinVersion = "1.7.10"

group "gr.datamation.mx.report"
version "1.10.1"

repositories {
    mavenCentral()
    maven {
        // this is needed to include github repos as dependencies
        url "https://jitpack.io"
    }
    maven {
        /*
         * The following properties should be defined in the ~/.gradle/gradle.properties
         * datamationRepoUrl=https://nexus.paymentcomponents.com/repository
         * datamationRepoUsername=internaluser
         * datamationRepoPassword=<internaluser password for the repo>
          */
        url "$datamationRepoUrl/internal"
        credentials {
            username = datamationRepoUsername
            password = datamationRepoPassword
        }
    }
}

sourceCompatibility = 1.8
targetCompatibility = 1.8

compileKotlin {
    kotlinOptions {
        jvmTarget = "${targetCompatibility}"
    }
}

configurations {
    provided
    minimal
    // these extensions are required to properly configure the build
    // so that gradle considers the `minimal`, `shadow` and `provided`
    // equivalent of `implementation` (officially defined) and includes
    // these dependencies in the classpath
    implementation.extendsFrom provided
    implementation.extendsFrom shadow
    implementation.extendsFrom minimal
}

dependencies {
    def itextVersion = "7.2.1"
    def jaxbVersion = "2.3.1"
    // the scopes `minimal`, `provided` are defined above (not part of gradle stdlib)
    // the scope `shadow` comes from the shadow plugin
    minimal "org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion"
    shadow "com.itextpdf:kernel:$itextVersion"
    shadow "com.itextpdf:io:$itextVersion"
    shadow "com.itextpdf:layout:$itextVersion"
    shadow "com.itextpdf:html2pdf:4.0.1"
    shadow "com.github.celtric:kotlin-html:0.1.4" // https://github.com/celtric/kotlin-html/tree/0.1.4
    provided "gr.datamation.mx:mx:24.10.0:mx-reporter"

    // JAXB dependencies for Java 9+ compatibility
    implementation "javax.xml.bind:jaxb-api:$jaxbVersion"
    implementation "org.glassfish.jaxb:jaxb-runtime:$jaxbVersion"
    implementation "javax.activation:activation:1.1.1"

    testImplementation "org.junit.jupiter:junit-jupiter-api:5.7.2"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:5.7.2"
}

/**
 * The following properties should be defined in the ~/.gradle/gradle.properties
 * systemProp.sonar.host.url=https://sonar.paymentcomponents.com
 * systemProp.sonar.login=your_access_token
 */
sonarqube {
    properties {
        property "sonar.junit.reportPaths", "$buildDir/test-results/test/"
        property "sonar.jacoco.reportPath", "$buildDir/jacoco/test"
        property "sonar.qualitygate.wait", "true"
        property "sonar.dependencyCheck.htmlReportPath", "$buildDir/reports/dependency-check-report.html"
    }
}

jacoco {
    toolVersion = "0.8.7"
}

test {
    useJUnitPlatform()
    finalizedBy jacocoTestReport
}

jacocoTestReport {
    dependsOn test // tests are required to run before generating the report
    reports {
        xml.required = true
        html.required = true
    }
}

jar {
    manifest {
        attributes ("Version" : "${project.version}")
    }
    archiveClassifier = "min"

    from {
        // dependencies marked as `minimal` will be included in the jar
        configurations.minimal.collect { it.isDirectory() ? it : zipTree(it) }
    }
    // https://youtrack.jetbrains.com/issue/DOC-13130
    duplicatesStrategy = DuplicatesStrategy.INCLUDE
}

shadowJar {
    // This task produces a fat jar containing the code with all
    // the dependencies included.
    // Warning: the resulting jar size is over 15MB - account for this when planning scaling and distribution.
    mergeServiceFiles()

    // The "shade" process rewrites the package names of the dependencies
    // to avoid collisions in the consumer modules.
    relocate("com.itextpdf", "shaded.com.itextpdf")
    relocate("org", "shaded.org")

    manifest {
        attributes ("Version" : "${project.version}")
    }

    // the dependency introduced as `shadow` will be included in the fat jar
    configurations = [project.configurations.shadow]

    archiveClassifier = "standalone"
    zip64 true // this is needed to overcome the restriction of up to 65k files per zip
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
            afterEvaluate {
                artifactId = "$project.name"
            }
        }
    }
    repositories {
        maven {
            name = 'internal'
            def releasesRepoUrl = "$datamationRepoUrl/internal"
            def snapshotsRepoUrl = "$datamationRepoUrl/snapshots"
            url = version.endsWith("SNAPSHOT") ? snapshotsRepoUrl : releasesRepoUrl
            credentials {
                username = datamationRepoUsername
                password = datamationRepoPassword
            }
        }
        maven {
            name = 'mx_reporter'
            url = "$datamationRepoUrl/mx_reporter"
            credentials {
                username = datamationRepoUsername
                password = datamationRepoPassword
            }
        }
    }
}

dependencyCheck {
    failOnError = false
}

tasks.jar.enabled = true // set to `false` to disable the `jar` task during `assemble`
tasks.assemble.dependsOn(tasks.shadowJar)
